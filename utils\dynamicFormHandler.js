const {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRow<PERSON><PERSON>er,
  <PERSON><PERSON>B<PERSON>er,
  ButtonStyle,
  ModalBuilder,
  TextInputBuilder,
  TextInputStyle,
  MessageFlags
} = require('discord.js');
const fs = require('fs');
const path = require('path');

const APPLICATION_CONFIG_FILE = path.join(__dirname, '..', 'application_config.json');
const FORM_SESSIONS_FILE = path.join(__dirname, '..', 'form_sessions.json');

class DynamicFormHandler {
  static loadConfig(guildId) {
    try {
      if (fs.existsSync(APPLICATION_CONFIG_FILE)) {
        const data = JSON.parse(fs.readFileSync(APPLICATION_CONFIG_FILE, 'utf8'));
        return data[guildId] || null;
      }
    } catch (error) {
      console.error('Error loading application config:', error);
    }
    return null;
  }

  static saveFormSession(guildId, userId, sessionD<PERSON>) {
    try {
      let data = {};
      if (fs.existsSync(FORM_SESSIONS_FILE)) {
        data = JSON.parse(fs.readFileSync(FORM_SESSIONS_FILE, 'utf8'));
      }
      
      if (!data[guildId]) {
        data[guildId] = {};
      }
      
      data[guildId][userId] = {
        ...sessionData,
        lastUpdated: new Date().toISOString()
      };
      
      fs.writeFileSync(FORM_SESSIONS_FILE, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('Error saving form session:', error);
    }
  }

  static getFormSession(guildId, userId) {
    try {
      if (fs.existsSync(FORM_SESSIONS_FILE)) {
        const data = JSON.parse(fs.readFileSync(FORM_SESSIONS_FILE, 'utf8'));
        return data[guildId]?.[userId] || null;
      }
    } catch (error) {
      console.error('Error loading form session:', error);
    }
    return null;
  }

  static clearFormSession(guildId, userId) {
    try {
      if (fs.existsSync(FORM_SESSIONS_FILE)) {
        const data = JSON.parse(fs.readFileSync(FORM_SESSIONS_FILE, 'utf8'));
        if (data[guildId]?.[userId]) {
          delete data[guildId][userId];
          fs.writeFileSync(FORM_SESSIONS_FILE, JSON.stringify(data, null, 2));
        }
      }
    } catch (error) {
      console.error('Error clearing form session:', error);
    }
  }

  // Enhanced start dynamic form for specific application type with professional workflow
  static async startDynamicFormForType(interaction, typeId, guildIdOverride = null) {
    console.log(`[DEBUG] Starting enhanced dynamic form for type: ${typeId}, user: ${interaction.user.tag}`);
    console.log(`[DEBUG] Guild ID from interaction: ${interaction.guildId}`);
    console.log(`[DEBUG] Guild ID override: ${guildIdOverride}`);

    // Use override guild ID if provided (for DM contexts), otherwise use interaction guild ID
    const effectiveGuildId = guildIdOverride || interaction.guildId;
    console.log(`[DEBUG] Using effective guild ID: ${effectiveGuildId}`);

    const config = this.loadConfig(effectiveGuildId);

    if (!config || !config.enabled) {
      const content = '❌ The application system is currently disabled.';
      if (interaction.deferred) {
        await interaction.editReply({ content });
      } else {
        await interaction.reply({ content, flags: MessageFlags.Ephemeral });
      }
      return;
    }

    // Get the specific application type
    const type = config.applicationTypes[typeId];
    if (!type || !type.questions || type.questions.length === 0) {
      const content = '❌ No questions have been configured for this application type.';
      if (interaction.deferred) {
        await interaction.editReply({ content });
      } else {
        await interaction.reply({ content, flags: MessageFlags.Ephemeral });
      }
      return;
    }

    // DEBUG: Log application type and questions
    console.log(`[DEBUG] Application type: ${type.name}, Questions count: ${type.questions.length}`);
    console.log(`[DEBUG] Questions:`, type.questions.map((q, i) => `${i}: ${q.question}`));

    // Initialize enhanced form session for specific type
    const sessionData = {
      guildId: effectiveGuildId, // Use effective guild ID instead of interaction.guildId
      userId: interaction.user.id,
      username: interaction.user.tag,
      typeId: typeId,
      typeName: type.name,
      questions: type.questions,
      answers: {},
      verificationResults: {},
      currentQuestionIndex: 0,
      useDM: false,
      startTime: new Date().toISOString(),
      status: 'in_progress',
      enhancedMode: true, // Flag for enhanced processing
      embedSequence: [], // Track sent embeds for professional flow
      responseValidation: {} // Track response validation
    };

    this.saveFormSession(interaction.guildId, interaction.user.id, sessionData);

    // Enhanced DM handling with professional workflow
    try {
      console.log(`[DEBUG] Attempting enhanced DM workflow for user: ${interaction.user.tag}`);
      const dmChannel = await interaction.user.createDM();

      // Professional welcome sequence
      await this.sendProfessionalWelcomeSequence(dmChannel, type, interaction.guild.name, sessionData);

      // Mark session as using enhanced DM mode
      sessionData.useDM = true;
      sessionData.waitingForDMResponse = true;
      sessionData.enhancedDMMode = true;
      this.saveFormSession(effectiveGuildId, interaction.user.id, sessionData);

      // Start professional question sequence
      await this.startProfessionalQuestionSequence(dmChannel, sessionData);

      // Confirm enhanced DM workflow started
      const content = '✨ **Enhanced Application Started!**\n\n📬 I\'ve sent you a professional application sequence in your DMs. Please check your direct messages to begin.\n\n🔄 The system will automatically process your responses and assign roles upon completion.';
      if (interaction.deferred) {
        await interaction.editReply({ content });
      } else {
        await interaction.reply({ content, flags: MessageFlags.Ephemeral });
      }

    } catch (error) {
      console.log('DM failed, using professional fallback system:', error.message);

      // Professional fallback to embed system with error recovery
      try {
        await this.startProfessionalEmbedFallback(interaction, sessionData, type);
      } catch (fallbackError) {
        console.error('Critical error in fallback system:', fallbackError);

        // Final error recovery
        const errorContent = '❌ Unable to start application. Please try again or contact an administrator.';
        if (interaction.deferred) {
          await interaction.editReply({ content: errorContent });
        } else {
          await interaction.reply({ content: errorContent, flags: MessageFlags.Ephemeral });
        }
      }
    }
  }

  static async startDynamicForm(interaction) {
    const config = this.loadConfig(interaction.guildId);
    
    if (!config || !config.enabled) {
      await interaction.reply({
        content: '❌ The application system is currently disabled.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    if (config.questions.length === 0) {
      await interaction.reply({
        content: '❌ No application questions have been configured.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Initialize form session
    const sessionData = {
      guildId: interaction.guildId,
      userId: interaction.user.id,
      username: interaction.user.tag,
      questions: config.questions,
      answers: {},
      verificationResults: {},
      currentQuestionIndex: 0,
      useDM: false,
      startTime: new Date().toISOString(),
      status: 'in_progress'
    };

    this.saveFormSession(interaction.guildId, interaction.user.id, sessionData);

    // Try to send form via DM first
    try {
      const dmChannel = await interaction.user.createDM();
      
      const welcomeEmbed = new EmbedBuilder()
        .setTitle('📋 Server Application Form')
        .setDescription(`Welcome to the ${interaction.guild.name} application process!`)
        .setColor(0x00AE86)
        .addFields([
          {
            name: '📝 Process Overview',
            value: `You will be asked ${config.questions.length} question${config.questions.length !== 1 ? 's' : ''} with real-time verification.`,
            inline: false
          },
          {
            name: '🔍 Verification',
            value: '',
            inline: false
          },
          {
            name: '⏱️ Estimated Time',
            value: '3-7 minutes',
            inline: true
          },
          {
            name: '🔒 Privacy',
            value: 'This conversation is private',
            inline: true
          }
        ])
        .setFooter({ text: 'Click "Start Form" to begin' });

      const startButton = new ActionRowBuilder()
        .addComponents(
          new ButtonBuilder()
            .setCustomId(`dynamic_form_start_${effectiveGuildId}_${interaction.user.id}`)
            .setLabel('Start Form')
            .setStyle(ButtonStyle.Primary)
            .setEmoji('📝')
        );

      await dmChannel.send({
        embeds: [welcomeEmbed],
        components: [startButton]
      });

      // Update session to use DM
      sessionData.useDM = true;
      this.saveFormSession(effectiveGuildId, interaction.user.id, sessionData);

      await interaction.reply({
        content: '✅ Application form sent to your DMs!',
        flags: MessageFlags.Ephemeral
      });

    } catch (error) {
      console.log('DMs are closed, using modal system');
      
      // DMs are closed, use modal system
      await this.startModalForm(interaction, sessionData);
    }
  }

  static async startModalForm(interaction, sessionData) {
    const embed = new EmbedBuilder()
      .setTitle('📋 Server Application Form')
      .setDescription('Your DMs appear to be closed, so we\'ll use an interactive form system here.')
      .setColor(0x00AE86)
      .addFields([
        {
          name: '📝 Process Overview',
          value: `You will be asked ${sessionData.questions.length} question${sessionData.questions.length !== 1 ? 's' : ''} with real-time verification.`,
          inline: false
        },
        {
          name: '🔍 Verification',
          value: '',
          inline: false
        },
        {
          name: '⚠️ Note',
          value: 'If there are many questions, they will be split across multiple forms.',
          inline: false
        }
      ])
      .setFooter({ text: 'Click "Start Form" to begin' });

    const startButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`dynamic_form_start_${interaction.guildId}_${interaction.user.id}`)
          .setLabel('Start Form')
          .setStyle(ButtonStyle.Primary)
          .setEmoji('📝')
      );

    await interaction.reply({
      embeds: [embed],
      components: [startButton],
      flags: MessageFlags.Ephemeral
    });
  }

  // Enhanced form start handler for professional embed fallback
  static async handleEnhancedFormStart(interaction) {
    // Immediately acknowledge the interaction
    await interaction.deferReply({ flags: MessageFlags.Ephemeral });

    const [, , , guildId, userId] = interaction.customId.split('_');
    const sessionData = this.getFormSession(guildId, userId);

    if (!sessionData) {
      await interaction.editReply({
        content: '❌ Form session not found. Please start the application process again.'
      });
      return;
    }

    // Start enhanced embed-based form
    await this.startEnhancedEmbedForm(interaction, sessionData);
  }

  // Enhanced form cancel handler
  static async handleEnhancedFormCancel(interaction) {
    await interaction.deferReply({ flags: MessageFlags.Ephemeral });

    const [, , , guildId, userId] = interaction.customId.split('_');

    // Clear any existing session
    this.clearFormSession(guildId, userId);

    // Send professional cancellation message
    const cancelEmbed = new EmbedBuilder()
      .setTitle('Application Cancelled')
      .setDescription('Your application has been cancelled. You can start a new application anytime.')
      .setColor(0x95A5A6) // Professional gray
      .setFooter({ text: 'Thank you for your interest' })
      .setTimestamp();

    await interaction.editReply({
      embeds: [cancelEmbed]
    });
  }

  // Start enhanced embed form for closed DMs
  static async startEnhancedEmbedForm(interaction, sessionData) {
    try {
      // Send professional start confirmation matching reference design
      const startEmbed = new EmbedBuilder()
        .setTitle('Application Started')
        .setDescription('Your first question will appear momentarily. Please be ready to respond.')
        .setColor(0x2F3136) // Dark theme color
        .setTimestamp();

      await interaction.reply({
        embeds: [startEmbed],
        flags: MessageFlags.Ephemeral
      });

      // Small delay for professional pacing
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Start with first question using enhanced modal system
      await this.showEnhancedQuestion(interaction, sessionData, 0);

    } catch (error) {
      console.error('Error starting enhanced embed form:', error);
      await interaction.followUp({
        content: '❌ Error starting enhanced form. Please try again.',
        flags: MessageFlags.Ephemeral
      });
    }
  }

  // Show enhanced question with professional styling
  static async showEnhancedQuestion(interaction, sessionData, questionIndex) {
    const question = sessionData.questions[questionIndex];

    if (question.type === 'choice') {
      // Enhanced choice question with professional buttons
      await this.showEnhancedChoiceQuestion(interaction, sessionData, questionIndex);
    } else {
      // Enhanced text question with professional modal
      await this.showEnhancedTextQuestion(interaction, sessionData, questionIndex);
    }
  }

  // Enhanced choice question display
  static async showEnhancedChoiceQuestion(interaction, sessionData, questionIndex) {
    const question = sessionData.questions[questionIndex];
    const progress = `${questionIndex + 1}/${sessionData.questions.length}`;

    const embed = new EmbedBuilder()
      .setTitle(`Question ${questionIndex + 1} of ${sessionData.questions.length}`)
      .setDescription(question.question)
      .setColor(0x5DADE2) // Light blue accent color
      .addFields([
        {
          name: `Type your answer below • ${questionIndex + 1}/${sessionData.questions.length}`,
          value: '\u200B', // Invisible character for spacing
          inline: false
        }
      ])
      .setTimestamp();

    // Create enhanced buttons for options
    const buttons = [];
    const maxButtonsPerRow = 5;
    const rows = [];

    for (let i = 0; i < question.options.length; i += maxButtonsPerRow) {
      const row = new ActionRowBuilder();
      const optionsSlice = question.options.slice(i, i + maxButtonsPerRow);

      optionsSlice.forEach((option, index) => {
        row.addComponents(
          new ButtonBuilder()
            .setCustomId(`enhanced_choice_${sessionData.guildId}_${sessionData.userId}_${questionIndex}_${i + index}`)
            .setLabel(option.length > 80 ? option.substring(0, 77) + '...' : option)
            .setStyle(ButtonStyle.Primary)
            .setEmoji('✅')
        );
      });

      rows.push(row);
    }

    await interaction.followUp({
      embeds: [embed],
      components: rows,
      flags: MessageFlags.Ephemeral
    });
  }

  // Enhanced text question display
  static async showEnhancedTextQuestion(interaction, sessionData, questionIndex) {
    const question = sessionData.questions[questionIndex];

    const modal = new ModalBuilder()
      .setCustomId(`enhanced_modal_${sessionData.guildId}_${sessionData.userId}_${questionIndex}`)
      .setTitle(`Question ${questionIndex + 1} - ${sessionData.typeName}`);

    const textInput = new TextInputBuilder()
      .setCustomId('enhanced_answer')
      .setLabel(question.question.length > 45 ? question.question.substring(0, 42) + '...' : question.question)
      .setStyle(question.multiline ? TextInputStyle.Paragraph : TextInputStyle.Short)
      .setRequired(question.required)
      .setMaxLength(question.maxLength || 1000);

    if (question.placeholder) {
      textInput.setPlaceholder(question.placeholder);
    }

    const actionRow = new ActionRowBuilder().addComponents(textInput);
    modal.addComponents(actionRow);

    await interaction.showModal(modal);
  }

  static async handleFormStart(interaction) {
    const [, , , guildId, userId] = interaction.customId.split('_');
    console.log(`[DEBUG] handleFormStart - extracted guildId: ${guildId}, userId: ${userId}`);
    console.log(`[DEBUG] handleFormStart - interaction.guildId: ${interaction.guildId}`);

    const sessionData = this.getFormSession(guildId, userId);

    if (!sessionData) {
      await interaction.reply({
        content: '❌ Form session not found. Please start the application process again.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Ensure session has the correct guild ID (important for DM context)
    if (!sessionData.guildId && guildId) {
      sessionData.guildId = guildId;
      this.saveFormSession(guildId, userId, sessionData);
    }

    console.log(`[DEBUG] handleFormStart - sessionData.guildId: ${sessionData.guildId}`);

    // Start with the first question
    await this.showNextQuestion(interaction, sessionData);
  }

  static async showNextQuestion(interaction, sessionData) {
    const currentIndex = sessionData.currentQuestionIndex;
    const question = sessionData.questions[currentIndex];

    // DEBUG: Add logging to understand what's happening
    console.log(`[DEBUG] showNextQuestion - currentIndex: ${currentIndex}, questions.length: ${sessionData.questions.length}, enhancedMode: ${sessionData.enhancedMode}`);
    console.log(`[DEBUG] Question exists: ${!!question}, sessionData.status: ${sessionData.status}`);

    if (!question) {
      // All questions completed, process the form
      console.log(`[DEBUG] No more questions - processing completed form`);
      await this.processCompletedForm(interaction, sessionData);
      return;
    }

    console.log(`[DEBUG] Showing question ${currentIndex + 1}: ${question.question}`);

    if (sessionData.useDM) {
      await this.showQuestionInDM(interaction, sessionData, question, currentIndex);
    } else {
      await this.showQuestionInModal(interaction, sessionData, question, currentIndex);
    }
  }

  static async showQuestionInDM(interaction, sessionData, question, questionIndex) {
    const dmChannel = await interaction.user.createDM();
    
    // Generate professional title based on question content
    const generateQuestionTitle = (questionText, index) => {
      const lowerQuestion = questionText.toLowerCase();
      
      if (lowerQuestion.includes('name')) {
        return `Step ${index + 1}: Your Full Name`;
      } else if (lowerQuestion.includes('email')) {
        return `Step ${index + 1}: Email Address`;
      } else if (lowerQuestion.includes('phone')) {
        return `Step ${index + 1}: Phone Number`;
      } else if (lowerQuestion.includes('group')) {
        return `Step ${index + 1}: Group Selection`;
      } else if (lowerQuestion.includes('age')) {
        return `Step ${index + 1}: Age Information`;
      } else if (lowerQuestion.includes('location') || lowerQuestion.includes('address')) {
        return `Step ${index + 1}: Location Details`;
      } else if (lowerQuestion.includes('experience')) {
        return `Step ${index + 1}: Experience Level`;
      } else if (lowerQuestion.includes('reason') || lowerQuestion.includes('why')) {
        return `Step ${index + 1}: Application Reason`;
      } else {
        return `Step ${index + 1}: Information Required`;
      }
    };

    // Generate professional description based on question type
    const generateQuestionDescription = (questionType) => {
      if (questionType === 'choice') {
        return 'Please select one of the available options.';
      } else {
        return 'Please provide your response below.';
      }
    };
    
    if (question.type === 'text') {
      // Text-based question in DM
      const embed = new EmbedBuilder()
        .setTitle(generateQuestionTitle(question.question, questionIndex))
        .setDescription(generateQuestionDescription(question.type))
        .setColor(0x3498DB);

      if (question.placeholder) {
        embed.addFields([
          {
            name: '💡 Example',
            value: question.placeholder,
            inline: false
          }
        ]);
      }

      await dmChannel.send({ embeds: [embed] });

    } else if (question.type === 'choice') {
      // Choice question in DM - show options as text
      const embed = new EmbedBuilder()
        .setTitle(generateQuestionTitle(question.question, questionIndex))
        .setDescription(generateQuestionDescription(question.type))
        .setColor(0x3498DB);

      // Add options as a field
      const optionsText = question.options.map((option, index) => 
        `**${index + 1}.** ${option}`
      ).join('\n');

      embed.addFields([
        {
          name: 'Available Options',
          value: optionsText,
          inline: false
        }
      ]);

      await dmChannel.send({ embeds: [embed] });
    }

    // Mark session as waiting for DM response
    sessionData.waitingForDMResponse = true;
    sessionData.currentQuestionIndex = questionIndex;
    this.saveFormSession(sessionData.guildId, sessionData.userId, sessionData);

    // Acknowledge the interaction to prevent "This interaction failed" error
    // Only for real button interactions, not mock interactions from DM messages
    if (!interaction.replied && !interaction.deferred && typeof interaction.deferUpdate === 'function') {
      await interaction.deferUpdate();
    }
  }

  static async showQuestionInModal(interaction, sessionData, question, questionIndex) {
    if (question.type === 'choice') {
      // For choice questions in server, show as buttons
      const embed = new EmbedBuilder()
        .setTitle(`📝 Question ${questionIndex + 1} of ${sessionData.questions.length}`)
        .setDescription(question.question)
        .setColor(0x3498DB)
        .addFields([
          {
            name: 'Instructions',
            value: 'Please select one of the options below.',
            inline: false
          },
          {
            name: 'Progress',
            value: `${questionIndex}/${sessionData.questions.length} questions completed`,
            inline: true
          }
        ]);

      const buttons = [];
      const maxButtonsPerRow = 5;
      const rows = [];
      
      for (let i = 0; i < question.options.length; i += maxButtonsPerRow) {
        const row = new ActionRowBuilder();
        const optionsSlice = question.options.slice(i, i + maxButtonsPerRow);
        
        optionsSlice.forEach((option, index) => {
          row.addComponents(
            new ButtonBuilder()
              .setCustomId(`dynamic_form_choice_${sessionData.guildId}_${sessionData.userId}_${questionIndex}_${i + index}`)
              .setLabel(option.length > 80 ? option.substring(0, 77) + '...' : option)
              .setStyle(ButtonStyle.Secondary)
              .setEmoji('📝')
          );
        });
        
        rows.push(row);
      }

      const method = interaction.replied || interaction.deferred ? 'editReply' : 'reply';
      await interaction[method]({
        embeds: [embed],
        components: rows,
        flags: MessageFlags.Ephemeral
      });

    } else {
      // For text questions, show modal
      const modal = new ModalBuilder()
        .setCustomId(`dynamic_form_modal_${sessionData.guildId}_${sessionData.userId}_${questionIndex}`)
        .setTitle(`Question ${questionIndex + 1} of ${sessionData.questions.length}`);

      const textInput = new TextInputBuilder()
        .setCustomId('answer')
        .setLabel(question.question)
        .setStyle(question.multiline ? TextInputStyle.Paragraph : TextInputStyle.Short)
        .setRequired(question.required)
        .setMaxLength(question.maxLength || 1000);

      if (question.placeholder) {
        textInput.setPlaceholder(question.placeholder);
      }

      const actionRow = new ActionRowBuilder().addComponents(textInput);
      modal.addComponents(actionRow);

      await interaction.showModal(modal);
    }
  }

  static async handleTextAnswer(interaction) {
    const [, , , guildId, userId, questionIndex] = interaction.customId.split('_');
    const sessionData = this.getFormSession(guildId, userId);
    
    if (!sessionData) {
      await interaction.reply({
        content: '❌ Form session not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const question = sessionData.questions[parseInt(questionIndex)];
    
    const modal = new ModalBuilder()
      .setCustomId(`dynamic_form_modal_${guildId}_${userId}_${questionIndex}`)
      .setTitle(`Question ${parseInt(questionIndex) + 1} of ${sessionData.questions.length}`);

    const textInput = new TextInputBuilder()
      .setCustomId('answer')
      .setLabel(question.question)
      .setStyle(question.multiline ? TextInputStyle.Paragraph : TextInputStyle.Short)
      .setRequired(question.required)
      .setMaxLength(question.maxLength || 1000);

    if (question.placeholder) {
      textInput.setPlaceholder(question.placeholder);
    }

    const actionRow = new ActionRowBuilder().addComponents(textInput);
    modal.addComponents(actionRow);

    await interaction.showModal(modal);
  }

  // Enhanced choice answer handler
  static async handleEnhancedChoiceAnswer(interaction) {
    const [, , guildId, userId, questionIndex, optionIndex] = interaction.customId.split('_');
    const sessionData = this.getFormSession(guildId, userId);

    if (!sessionData) {
      await interaction.reply({
        content: '❌ Form session not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const question = sessionData.questions[parseInt(questionIndex)];
    const selectedOption = question.options[parseInt(optionIndex)];

    // Process the enhanced choice answer
    await this.processEnhancedAnswer(interaction, sessionData, parseInt(questionIndex), selectedOption);
  }

  // Enhanced modal submit handler
  static async handleEnhancedModalSubmit(interaction) {
    const [, , guildId, userId, questionIndex] = interaction.customId.split('_');
    const sessionData = this.getFormSession(guildId, userId);

    if (!sessionData) {
      await interaction.reply({
        content: '❌ Form session not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const answer = interaction.fields.getTextInputValue('enhanced_answer');

    // Process the enhanced text answer
    await this.processEnhancedAnswer(interaction, sessionData, parseInt(questionIndex), answer);
  }

  // Process enhanced answer with professional workflow
  static async processEnhancedAnswer(interaction, sessionData, questionIndex, answer) {
    try {
      const question = sessionData.questions[questionIndex];

      // Enhanced validation
      const validationResult = await this.validateEnhancedResponse(answer, question, sessionData);

      if (!validationResult.valid) {
        await interaction.reply({
          content: `❌ **Validation Error**\n\n${validationResult.error}`,
          flags: MessageFlags.Ephemeral
        });
        return;
      }

      // Record the validated answer
      sessionData.answers[questionIndex] = validationResult.processedAnswer;
      sessionData.responseValidation[questionIndex] = {
        originalAnswer: answer,
        processedAnswer: validationResult.processedAnswer,
        timestamp: new Date().toISOString(),
        valid: true
      };

      // Send professional acknowledgment
      const progress = `${questionIndex + 1}/${sessionData.questions.length}`;
      const isLastQuestion = questionIndex + 1 >= sessionData.questions.length;

      if (isLastQuestion) {
        // All questions completed
        const completionEmbed = new EmbedBuilder()
          .setTitle('✅ Application Complete!')
          .setDescription('Thank you for completing all questions. Your application is now being processed automatically.')
          .setColor(0x00AE86)
          .addFields([
            {
              name: '📊 Final Progress',
              value: `${progress} questions completed (100%)`,
              inline: true
            },
            {
              name: '🔄 Processing Status',
              value: 'Automatic verification and role assignment in progress...',
              inline: true
            },
            {
              name: '⚡ Next Steps',
              value: '• Verifying responses\n• Assigning appropriate roles\n• Sending confirmation',
              inline: false
            }
          ])
          .setFooter({ text: 'Enhanced Application System • Processing Complete' })
          .setTimestamp();

        await interaction.reply({
          embeds: [completionEmbed],
          flags: MessageFlags.Ephemeral
        });

        // Mark as completed and process
        sessionData.status = 'completed';
        sessionData.completedAt = new Date().toISOString();
        this.saveFormSession(sessionData.guildId, sessionData.userId, sessionData);

        // Process the completed enhanced application
        await this.processCompletedForm(interaction, sessionData);

      } else {
        // Continue to next question
        const progressEmbed = new EmbedBuilder()
          .setTitle('✓ Response Recorded')
          .setDescription(`Question ${questionIndex + 1} completed successfully.`)
          .setColor(0x3498DB)
          .addFields([
            {
              name: '📊 Progress',
              value: `${progress} • ${Math.round(((questionIndex + 1) / sessionData.questions.length) * 100)}% complete`,
              inline: true
            },
            {
              name: '⏭️ Next Question',
              value: 'Preparing next question...',
              inline: true
            }
          ])
          .setFooter({ text: 'Enhanced Application System • In Progress' });

        await interaction.reply({
          embeds: [progressEmbed],
          flags: MessageFlags.Ephemeral
        });

        // Small delay for professional pacing
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Show next question
        await this.showEnhancedQuestion(interaction, sessionData, questionIndex + 1);
      }

    } catch (error) {
      console.error('Error processing enhanced answer:', error);
      await interaction.reply({
        content: '❌ Error processing your response. Please try again.',
        flags: MessageFlags.Ephemeral
      });
    }
  }

  static async handleModalSubmit(interaction) {
    const [, , , guildId, userId, questionIndex] = interaction.customId.split('_');
    const sessionData = this.getFormSession(guildId, userId);

    if (!sessionData) {
      await interaction.reply({
        content: '❌ Form session not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const answer = interaction.fields.getTextInputValue('answer');
    await this.processAnswer(interaction, sessionData, parseInt(questionIndex), answer);
  }

  static async handleChoiceAnswer(interaction) {
    const parts = interaction.customId.split('_');
    const guildId = parts[3];
    const userId = parts[4];
    const questionIndex = parseInt(parts[5]);
    const optionIndex = parseInt(parts[6]);
    
    const sessionData = this.getFormSession(guildId, userId);
    
    if (!sessionData) {
      await interaction.reply({
        content: '❌ Form session not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const question = sessionData.questions[questionIndex];
    const answer = question.options[optionIndex];
    
    await this.processAnswer(interaction, sessionData, questionIndex, answer);
  }

  static async processAnswer(interaction, sessionData, questionIndex, answer) {
    console.log(`[DEBUG] Processing answer for question ${questionIndex}: "${answer}", useDM: ${sessionData.useDM}`);
    
    const question = sessionData.questions[questionIndex];
    
    // Perform verification silently in the backend
    let verificationResult = { valid: true, message: 'No verification required' };
    
    if (question.verifyInChannels) {
      const ApplicationHandler = require('./applicationHandler');
      verificationResult = await ApplicationHandler.verifyUserDataInChannels(answer, question, interaction);
      
      if (!verificationResult.valid && question.verificationMode === 'required') {
        // Verification failed for required question - fail silently, handle at the end
        sessionData.answers[questionIndex] = answer;
        sessionData.verificationResults[questionIndex] = verificationResult;
        sessionData.currentQuestionIndex = questionIndex + 1;
        sessionData.waitingForDMResponse = false;
        this.saveFormSession(sessionData.guildId, sessionData.userId, sessionData);

        // For verification failures, don't send channel messages for DM users
        if (!sessionData.useDM) {
          const isLastQuestion = questionIndex === sessionData.questions.length - 1;
          await this.sendAnswerRecordedMessage(interaction, sessionData, isLastQuestion);
        }
        
        // Don't automatically continue - this will be handled by the DM response
        return;
      }
    }

    // Save answer and verification result silently
    sessionData.answers[questionIndex] = answer;
    sessionData.verificationResults[questionIndex] = verificationResult;
    sessionData.currentQuestionIndex = questionIndex + 1;
    sessionData.waitingForDMResponse = false;
    this.saveFormSession(sessionData.guildId, sessionData.userId, sessionData);

    // Check if this is the final question
    const isLastQuestion = questionIndex === sessionData.questions.length - 1;
    
    if (isLastQuestion) {
      // Final question - process the completed form
      await this.processCompletedForm(interaction, sessionData);
    } else {
      // Not the final question - for DM users, don't send channel messages
      if (!sessionData.useDM) {
        // Only send confirmation messages for modal users
        await this.sendAnswerRecordedMessage(interaction, sessionData, false);
      }
      // DO NOT automatically continue - wait for user's next response
    }
  }

  // Enhanced professional welcome sequence for DMs - simplified to avoid spam
  static async sendProfessionalWelcomeSequence(dmChannel, type, guildName, sessionData) {
    // Skip sending welcome embed since confirmation was already shown in channel
    // Just add to embed sequence tracking for consistency
    sessionData.embedSequence.push({
      type: 'welcome',
      timestamp: new Date().toISOString(),
      embed: 'Professional Welcome (Skipped - Confirmation shown in channel)'
    });
  }

  // Start professional question sequence - simplified to avoid spam
  static async startProfessionalQuestionSequence(dmChannel, sessionData) {
    // Skip the "Ready to Begin" embed to reduce spam
    // Start directly with the first question
    await this.sendProfessionalQuestion(dmChannel, sessionData, 0);

    // CRITICAL FIX: Mark session as waiting for DM response after sending first question
    sessionData.waitingForDMResponse = true;
    sessionData.currentQuestionIndex = 0;
    this.saveFormSession(sessionData.guildId, sessionData.userId, sessionData);
  }

  // Professional embed fallback for closed DMs with error recovery
  static async startProfessionalEmbedFallback(interaction, sessionData, type) {
    sessionData.useDM = false;
    sessionData.enhancedEmbedMode = true;
    this.saveFormSession(sessionData.guildId, sessionData.userId, sessionData);

    // Check if interaction is still valid
    if (!interaction.replied && !interaction.deferred) {
      try {
        await interaction.deferReply({ flags: MessageFlags.Ephemeral });
      } catch (error) {
        console.error('Failed to defer interaction in fallback:', error);
        // If we can't defer, the interaction has likely expired
        if (error.code === 10062 || error.message.includes('Unknown interaction')) {
          throw new Error('Interaction token expired - cannot start fallback');
        }
      }
    }

    // Send professional, clean application interface matching reference design
    const fallbackEmbed = new EmbedBuilder()
      .setTitle(`${type.name} Application`)
      .setDescription('Your application process has begun. Please answer each question thoroughly and professionally.')
      .setColor(0x2F3136) // Dark theme color
      .addFields([
        {
          name: 'Guidelines',
          value: '• You have 60 minutes to complete this application\n• Answer each question as it appears\n• Be honest and detailed in your responses\n• You can cancel the application at any time',
          inline: false
        },
        {
          name: '─────────────────────────',
          value: '**Process**\n• Total questions: **3**\n• Each question will appear one at a time\n• Type your answer and send it as a message',
          inline: false
        }
      ])
      .setFooter({ text: '© 2025 Application System • Confidential • 7/23/2025 2:36 AM' })
      .setTimestamp();

    const actionRow = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`enhanced_form_start_${interaction.guildId}_${interaction.user.id}`)
          .setLabel('Start Application')
          .setStyle(ButtonStyle.Success)
          .setEmoji('▶️'),
        new ButtonBuilder()
          .setCustomId(`enhanced_form_cancel_${interaction.guildId}_${interaction.user.id}`)
          .setLabel('Cancel')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('✖️')
      );

    // Use appropriate response method based on interaction state with error recovery
    try {
      if (interaction.deferred) {
        await interaction.editReply({
          embeds: [fallbackEmbed],
          components: [actionRow]
        });
      } else {
        await interaction.reply({
          embeds: [fallbackEmbed],
          components: [actionRow],
          flags: MessageFlags.Ephemeral
        });
      }
    } catch (error) {
      console.error('Failed to send fallback embed:', error);

      // If interaction failed due to token expiration, try DM fallback
      if (error.code === 10062 || error.message.includes('Unknown interaction')) {
        console.log('Interaction expired, attempting DM fallback for application start');
        try {
          const dmChannel = await interaction.user.createDM();
          await dmChannel.send({
            content: '⚠️ **Application System Notice**\n\nThere was a technical issue with the application interface. Your application session has been prepared. Please contact an administrator to continue with your application.',
            embeds: [fallbackEmbed],
            components: [actionRow]
          });
        } catch (dmError) {
          console.error('DM fallback also failed:', dmError);
          throw new Error('Both interaction and DM fallback failed');
        }
      } else {
        throw error;
      }
    }
  }

  static async handleDMMessage(message) {
    // Only process DM messages from users (not bots)
    if (message.author.bot || message.channel.type !== 1) return;

    console.log(`[DEBUG] DM message received from ${message.author.tag}: ${message.content}`);

    // Check if user has an active form session waiting for DM response
    const allSessions = this.getAllFormSessions();
    let userSession = null;
    let guildId = null;

    console.log(`[DEBUG] All sessions:`, allSessions);

    for (const [guild, users] of Object.entries(allSessions)) {
      if (users[message.author.id] && users[message.author.id].waitingForDMResponse) {
        userSession = users[message.author.id];
        guildId = guild;
        console.log(`[DEBUG] Found session for user ${message.author.tag} in guild ${guild}`);
        console.log(`[DEBUG] Session data:`, {
          enhancedMode: userSession.enhancedMode,
          waitingForDMResponse: userSession.waitingForDMResponse,
          currentQuestionIndex: userSession.currentQuestionIndex,
          questionsLength: userSession.questions.length
        });
        break;
      }
    }

    if (!userSession) {
      console.log(`[DEBUG] No active session found for user ${message.author.tag}`);
      return; // No active session
    }

    // Enhanced DM message handling
    if (userSession.enhancedMode) {
      console.log(`[DEBUG] Routing to enhanced DM response handler`);
      await this.handleEnhancedDMResponse(message, userSession, guildId);
      return;
    }

    // Legacy DM handling (keep for backward compatibility)
    console.log(`[DEBUG] Using legacy DM handling for user ${message.author.tag}`);
    const currentQuestion = userSession.questions[userSession.currentQuestionIndex];
    if (!currentQuestion) {
      console.log(`[DEBUG] No current question found in legacy handler`);
      return;
    }

    let answer = message.content.trim();

    // Handle choice questions - allow number selection or full text
    if (currentQuestion.type === 'choice') {
      const numberMatch = answer.match(/^(\d+)$/);
      if (numberMatch) {
        const optionIndex = parseInt(numberMatch[1]) - 1;
        if (optionIndex >= 0 && optionIndex < currentQuestion.options.length) {
          answer = currentQuestion.options[optionIndex];
        } else {
          await message.reply('❌ Invalid option number. Please try again.');
          return;
        }
      } else {
        // Check if the answer matches any of the options
        const matchedOption = currentQuestion.options.find(option =>
          option.toLowerCase() === answer.toLowerCase()
        );
        if (matchedOption) {
          answer = matchedOption;
        } else {
          await message.reply('❌ Invalid option. Please type the number or exact option text.');
          return;
        }
      }
    }

    // Validate required fields
    if (currentQuestion.required && !answer) {
      await message.reply('❌ This question is required. Please provide an answer.');
      return;
    }

    // Create a mock interaction object for processing
    const mockInteraction = {
      user: message.author,
      guild: message.client.guilds.cache.get(guildId),
      guildId: guildId,
      replied: false,
      deferred: false
    };

    // Process the answer
    await this.processAnswer(mockInteraction, userSession, userSession.currentQuestionIndex, answer);

    // After processing the answer, send confirmation and continue to next question if not completed
    const updatedSession = this.getFormSession(guildId, message.author.id);
    if (updatedSession && updatedSession.status === 'in_progress') {
      // Send answer confirmation before next question
      await this.sendAnswerConfirmation(message.channel, userSession.currentQuestionIndex, userSession.questions.length);

      console.log(`[DEBUG] Legacy handler calling showNextQuestion`);
      await this.showNextQuestion(mockInteraction, updatedSession);
    }
  }

  // Enhanced DM response handling with professional workflow
  static async handleEnhancedDMResponse(message, userSession, guildId) {
    const currentQuestion = userSession.questions[userSession.currentQuestionIndex];
    if (!currentQuestion) return;

    let answer = message.content.trim();

    // Enhanced validation and processing
    const validationResult = await this.validateEnhancedResponse(answer, currentQuestion, userSession);

    if (!validationResult.valid) {
      await this.sendProfessionalValidationError(message.channel, validationResult.error, currentQuestion);
      return;
    }

    // Process the validated answer
    answer = validationResult.processedAnswer;

    // Record response with enhanced tracking
    userSession.answers[userSession.currentQuestionIndex] = answer;
    userSession.responseValidation[userSession.currentQuestionIndex] = {
      originalAnswer: message.content.trim(),
      processedAnswer: answer,
      timestamp: new Date().toISOString(),
      valid: true
    };

    // Send professional acknowledgment
    await this.sendProfessionalAcknowledgment(message.channel, userSession.currentQuestionIndex + 1, userSession.questions.length);

    // Move to next question or complete
    userSession.currentQuestionIndex++;
    userSession.waitingForDMResponse = false;
    this.saveFormSession(guildId, userSession.userId, userSession);

    if (userSession.currentQuestionIndex >= userSession.questions.length) {
      // All questions completed - start enhanced processing
      await this.processEnhancedCompletedForm(message, userSession, guildId);
    } else {
      // Continue to next question with professional pacing
      await new Promise(resolve => setTimeout(resolve, 1000));
      await this.sendProfessionalQuestion(message.channel, userSession, userSession.currentQuestionIndex);
      userSession.waitingForDMResponse = true;
      this.saveFormSession(guildId, userSession.userId, userSession);
    }
  }

  // Send professional question with clean, enterprise-level formatting
  static async sendProfessionalQuestion(dmChannel, sessionData, questionIndex) {
    const question = sessionData.questions[questionIndex];
    const progress = questionIndex + 1;
    const total = sessionData.questions.length;

    // First send the "Application Started" embed if it's the first question
    if (questionIndex === 0) {
      const startedEmbed = new EmbedBuilder()
        .setTitle('Application Started')
        .setDescription('Your first question will appear momentarily. Please be ready to respond.')
        .setColor(0x2F3136)
        .setTimestamp();

      await dmChannel.send({ embeds: [startedEmbed] });

      // Small delay before showing the first question
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // Send the question embed with improved highlighting
    const questionEmbed = new EmbedBuilder()
      .setTitle(`Question ${progress} of ${total}`)
      .setDescription(`**${question.question}**`) // Bold the question for better visibility
      .setColor(0x5DADE2) // Light blue accent color
      .addFields([
        {
          name: '─────────────────────────',
          value: `**Instructions**\nType your answer below • ${progress}/${total}`,
          inline: false
        }
      ])
      .setTimestamp();

    // Add clean instructions based on question type
    if (question.type === 'choice') {
      const optionsText = question.options.map((option, index) =>
        `**${index + 1}.** ${option}`
      ).join('\n');

      questionEmbed.addFields([
        {
          name: '📋 Available Options',
          value: optionsText,
          inline: false
        },
        {
          name: '💡 How to Answer',
          value: 'Reply with the **number** or **exact text** of your choice',
          inline: false
        }
      ]);
    } else {
      if (question.placeholder) {
        questionEmbed.addFields([
          {
            name: '💡 Example Answer',
            value: `\`${question.placeholder}\``,
            inline: false
          }
        ]);
      }
    }

    await dmChannel.send({ embeds: [questionEmbed] });

    // Track in embed sequence
    sessionData.embedSequence.push({
      type: 'question',
      questionIndex: questionIndex,
      timestamp: new Date().toISOString(),
      embed: `Question ${questionIndex + 1}`
    });
  }

  // Send answer confirmation embed matching reference design
  static async sendAnswerConfirmation(dmChannel, questionIndex, totalQuestions) {
    const confirmationEmbed = new EmbedBuilder()
      .setTitle('✓ Answer recorded. Next question coming up...')
      .setColor(0x00AE86) // Green confirmation color
      .setTimestamp();

    await dmChannel.send({ embeds: [confirmationEmbed] });

    // Small delay before next question
    await new Promise(resolve => setTimeout(resolve, 1500));
  }

  // Create a professional progress bar
  static createProgressBar(current, total, length = 10) {
    const percentage = current / total;
    const filled = Math.round(length * percentage);
    const empty = length - filled;

    const filledBar = '█'.repeat(filled);
    const emptyBar = '░'.repeat(empty);

    return `${filledBar}${emptyBar}`;
  }

  // Get configurable message for application type or fallback to global
  static getConfigurableMessage(config, typeId, messageType) {
    // Try to get type-specific message first
    if (typeId && config?.applicationTypes?.[typeId]?.messages?.[messageType]) {
      return config.applicationTypes[typeId].messages[messageType];
    }

    // Fallback to global messages
    if (config?.messages?.[messageType]) {
      return config.messages[messageType];
    }

    // Final fallback to defaults
    const defaults = {
      success: {
        title: '✅ Application Approved',
        description: 'Congratulations {username}! Your {application_type} has been approved.',
        footer: 'Welcome to the community!'
      },
      pending: {
        title: '⏳ Application Under Review',
        description: 'Your {application_type} is currently under review by our staff.',
        footer: 'You will be contacted if additional information is needed.'
      },
      denial: {
        title: '❌ Application Denied',
        description: 'Your {application_type} could not be approved at this time.',
        footer: 'Please contact staff if you have questions.'
      }
    };

    return defaults[messageType] || defaults.success;
  }

  // Enhanced response validation
  static async validateEnhancedResponse(answer, question, userSession) {
    // Basic validation
    if (question.required && (!answer || answer.trim().length === 0)) {
      return {
        valid: false,
        error: 'This question is required. Please provide an answer.',
        processedAnswer: null
      };
    }

    // Choice question validation
    if (question.type === 'choice') {
      const numberMatch = answer.match(/^(\d+)$/);
      if (numberMatch) {
        const optionIndex = parseInt(numberMatch[1]) - 1;
        if (optionIndex >= 0 && optionIndex < question.options.length) {
          return {
            valid: true,
            processedAnswer: question.options[optionIndex]
          };
        } else {
          return {
            valid: false,
            error: `Invalid option number. Please choose between 1 and ${question.options.length}.`,
            processedAnswer: null
          };
        }
      } else {
        // Check if the answer matches any of the options
        const matchedOption = question.options.find(option =>
          option.toLowerCase() === answer.toLowerCase()
        );
        if (matchedOption) {
          return {
            valid: true,
            processedAnswer: matchedOption
          };
        } else {
          return {
            valid: false,
            error: 'Invalid option. Please type the number or exact option text.',
            processedAnswer: null
          };
        }
      }
    }

    // Text validation
    if (question.maxLength && answer.length > question.maxLength) {
      return {
        valid: false,
        error: `Response too long. Maximum ${question.maxLength} characters allowed.`,
        processedAnswer: null
      };
    }

    return {
      valid: true,
      processedAnswer: answer.trim()
    };
  }

  static getAllFormSessions() {
    try {
      if (fs.existsSync(FORM_SESSIONS_FILE)) {
        return JSON.parse(fs.readFileSync(FORM_SESSIONS_FILE, 'utf8'));
      }
    } catch (error) {
      console.error('Error loading all form sessions:', error);
    }
    return {};
  }

  // Send professional validation error
  static async sendProfessionalValidationError(dmChannel, errorMessage, question) {
    const errorEmbed = new EmbedBuilder()
      .setTitle('⚠️ Response Validation')
      .setDescription(errorMessage)
      .setColor(0xE74C3C)
      .addFields([
        {
          name: '🔄 Please Try Again',
          value: 'Provide a valid response to continue with your application.',
          inline: false
        }
      ]);

    if (question.type === 'choice') {
      const optionsText = question.options.map((option, index) =>
        `**${index + 1}.** ${option}`
      ).join('\n');

      errorEmbed.addFields([
        {
          name: '📝 Available Options',
          value: optionsText,
          inline: false
        }
      ]);
    }

    errorEmbed.setFooter({ text: 'Professional Application System • Validation Error' });

    await dmChannel.send({ embeds: [errorEmbed] });
  }

  // Send professional acknowledgment
  static async sendProfessionalAcknowledgment(dmChannel, currentQuestionNumber, totalQuestions) {
    const isLastQuestion = currentQuestionNumber === totalQuestions;

    if (isLastQuestion) {
      const completionEmbed = new EmbedBuilder()
        .setTitle('✅ Application Complete!')
        .setDescription('Thank you for completing all questions. Your application is now being processed automatically.')
        .setColor(0x00AE86)
        .addFields([
          {
            name: '🔄 Next Steps',
            value: '• Automatic verification in progress\n• Role assignment processing\n• Confirmation message incoming',
            inline: false
          }
        ])
        .setFooter({ text: 'Professional Application System • Processing Complete' });

      await dmChannel.send({ embeds: [completionEmbed] });
    } else {
      const progressEmbed = new EmbedBuilder()
        .setTitle('✓ Response Recorded')
        .setDescription(`Question ${currentQuestionNumber} of ${totalQuestions} completed.`)
        .setColor(0x3498DB)
        .addFields([
          {
            name: '📊 Progress',
            value: `${Math.round((currentQuestionNumber / totalQuestions) * 100)}% complete`,
            inline: true
          },
          {
            name: '⏭️ Next',
            value: 'Preparing next question...',
            inline: true
          }
        ])
        .setFooter({ text: 'Professional Application System • In Progress' });

      await dmChannel.send({ embeds: [progressEmbed] });
    }
  }

  // Enhanced completed form processing
  static async processEnhancedCompletedForm(message, sessionData, guildId) {
    console.log(`[ENHANCED] Processing completed form for user: ${sessionData.username}`);

    // Create mock interaction for processing
    const mockInteraction = {
      user: message.author,
      guild: message.client.guilds.cache.get(guildId),
      guildId: guildId,
      replied: false,
      deferred: false
    };

    // Process with enhanced workflow - no intermediate processing embeds
    const config = this.loadConfig(guildId);
    await this.processEnhancedApplication(mockInteraction, sessionData, config);
  }

  static async handleRetry(interaction) {
    const [, , , guildId, userId, questionIndex] = interaction.customId.split('_');
    const sessionData = this.getFormSession(guildId, userId);

    if (!sessionData) {
      await interaction.reply({
        content: '❌ Form session not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Reset to the failed question
    sessionData.currentQuestionIndex = parseInt(questionIndex);
    this.saveFormSession(guildId, userId, sessionData);

    await this.showNextQuestion(interaction, sessionData);
  }

  // Enhanced application processing with professional workflow
  static async processEnhancedApplication(interaction, sessionData, config) {
    console.log(`[ENHANCED] Starting enhanced application processing for ${sessionData.username}`);

    try {
      // Enhanced verification and role assignment
      const processingResult = await this.performEnhancedProcessing(interaction, sessionData, config);

      // Send professional completion message
      await this.sendEnhancedCompletionMessage(interaction, sessionData, processingResult);

      // Enhanced logging to panels channel
      await this.logEnhancedApplicationToPanel(interaction, sessionData, processingResult, config);

      // Clean up session
      this.clearFormSession(sessionData.guildId, sessionData.userId);

      console.log(`[ENHANCED] Successfully processed application for ${sessionData.username}`);

    } catch (error) {
      console.error('[ENHANCED] Error in enhanced processing:', error);
      await this.sendEnhancedErrorResponse(interaction, sessionData);
    }
  }

  // Perform enhanced processing with comprehensive validation and role assignment
  static async performEnhancedProcessing(interaction, sessionData, config) {
    const result = {
      success: false,
      assignedRoles: [],
      verificationResults: {},
      failedVerifications: [],
      processingLog: []
    };

    try {
      // Get the correct application type configuration
      let applicationType = null;
      if (sessionData.typeId && config.applicationTypes && config.applicationTypes[sessionData.typeId]) {
        applicationType = config.applicationTypes[sessionData.typeId];
      }

      // Enhanced verification process
      for (const [questionIndex, answer] of Object.entries(sessionData.answers)) {
        const question = sessionData.questions[parseInt(questionIndex)];

        if (question.verifyInChannels) {
          const ApplicationHandler = require('./applicationHandler');
          const verificationResult = await ApplicationHandler.verifyUserDataInChannels(answer, question, interaction);

          result.verificationResults[questionIndex] = verificationResult;

          if (!verificationResult.valid && question.verificationMode === 'required') {
            result.failedVerifications.push({
              question: question.question,
              answer: answer,
              error: verificationResult.error
            });
          }
        }
      }

      // If verification failed, return early
      if (result.failedVerifications.length > 0) {
        result.success = false;
        result.processingLog.push('Verification failed - application denied');
        return result;
      }

      // Enhanced role assignment
      const member = await interaction.guild.members.fetch(sessionData.userId);

      for (const [questionIndex, answer] of Object.entries(sessionData.answers)) {
        // Get role assignments from the correct source
        let roleAssignments = null;
        if (applicationType && applicationType.roleAssignments) {
          roleAssignments = applicationType.roleAssignments[questionIndex];
        } else if (config.roleAssignments) {
          roleAssignments = config.roleAssignments[questionIndex];
        }

        if (roleAssignments) {
          let roleId = null;

          // Enhanced role matching logic
          if (roleAssignments[answer]) {
            roleId = roleAssignments[answer];
          } else if (roleAssignments['__text_answer__']) {
            roleId = roleAssignments['__text_answer__'];
          } else {
            const normalizedAnswer = answer.toLowerCase().trim();
            roleId = roleAssignments[normalizedAnswer];
          }

          if (roleId) {
            try {
              const role = await interaction.guild.roles.fetch(roleId);
              if (role) {
                await member.roles.add(role, `Enhanced application processing - ${sessionData.typeName || 'Application'}`);
                result.assignedRoles.push({
                  roleId: roleId,
                  roleName: role.name,
                  questionIndex: questionIndex,
                  answer: answer
                });
                result.processingLog.push(`Assigned role: ${role.name} for answer: "${answer}"`);
              }
            } catch (error) {
              console.error(`Error assigning role ${roleId}:`, error);
              result.processingLog.push(`Failed to assign role ${roleId}: ${error.message}`);
            }
          }
        }
      }

      result.success = true;
      result.processingLog.push(`Successfully processed application with ${result.assignedRoles.length} role assignments`);

    } catch (error) {
      console.error('Error in enhanced processing:', error);
      result.success = false;
      result.processingLog.push(`Processing error: ${error.message}`);
    }

    return result;
  }

  static async processCompletedForm(interaction, sessionData) {
    // DEBUG: Log why processCompletedForm is being called
    console.log(`[DEBUG] processCompletedForm called for user: ${sessionData.username}`);
    console.log(`[DEBUG] Session data:`, {
      currentQuestionIndex: sessionData.currentQuestionIndex,
      questionsLength: sessionData.questions.length,
      enhancedMode: sessionData.enhancedMode,
      status: sessionData.status,
      waitingForDMResponse: sessionData.waitingForDMResponse
    });

    // Check if enhanced mode is enabled
    if (sessionData.enhancedMode) {
      console.log(`[DEBUG] Processing enhanced application`);
      const config = this.loadConfig(sessionData.guildId);
      await this.processEnhancedApplication(interaction, sessionData, config);
      return;
    }

    // Legacy processing
    const config = this.loadConfig(sessionData.guildId);

    // For multi-type applications, ensure we have the correct type data
    if (sessionData.typeId && config.applicationTypes && config.applicationTypes[sessionData.typeId]) {
      const type = config.applicationTypes[sessionData.typeId];
      sessionData.questions = type.questions; // Ensure we have the latest questions
      sessionData.typeName = type.name;
    }

    // Check current queue load to determine processing strategy
    const currentQueueSize = this.getQueueSize();

    if (currentQueueSize === 0) {
      // Queue is empty - process immediately without queue delays
      console.log('🚀 Queue empty - processing form immediately');
      await this.processApplicationImmediately(interaction, sessionData, config);
    } else {
      // Queue has items - use standard queue processing
      console.log(`📋 Queue has ${currentQueueSize} items - using queue system`);
      await this.addToProcessingQueue(interaction, sessionData, config);
      await this.processApplicationFromQueue(interaction, sessionData, config);
    }
  }

  // Send single final result embed using configurable messages
  static async sendEnhancedCompletionMessage(interaction, sessionData, processingResult) {
    const config = this.loadConfig(sessionData.guildId);
    const dmChannel = sessionData.useDM ? await interaction.user.createDM() : null;

    // Determine message type based on result
    let messageType, statusText, nextSteps;
    if (processingResult.success) {
      messageType = 'success';
      statusText = 'Approved';
      nextSteps = 'You now have access to the appropriate areas. Welcome!';
    } else {
      messageType = 'pending';
      statusText = 'Pending Review';
      nextSteps = 'Please wait for staff review. You may be contacted for additional information.';
    }

    // Get configurable message or use defaults
    const messageConfig = this.getConfigurableMessage(config, sessionData.typeId, messageType);

    // Create placeholders for dynamic content
    const placeholders = {
      '{username}': interaction.user.displayName || interaction.user.username,
      '{application_type}': sessionData.typeName || 'Application',
      '{status}': statusText,
      '{timestamp}': new Date().toLocaleString(),
      '{roles_assigned}': processingResult.assignedRoles.length > 0
        ? processingResult.assignedRoles.map(role => `• ${role.roleName}`).join('\n')
        : 'No specific roles assigned',
      '{next_steps}': nextSteps
    };

    // Replace placeholders in message
    const title = this.replacePlaceholders(messageConfig.title, placeholders);
    const description = this.replacePlaceholders(messageConfig.description, placeholders);
    const footer = messageConfig.footer ? this.replacePlaceholders(messageConfig.footer, placeholders) : null;

    // Create single final result embed with appropriate color
    const colors = {
      success: 0x00AE86,  // Green
      pending: 0xF39C12,  // Orange
      denial: 0xE74C3C    // Red
    };

    const resultEmbed = new EmbedBuilder()
      .setTitle(title)
      .setDescription(description)
      .setColor(colors[messageType] || colors.success)
      .addFields([
        {
          name: 'Application Status',
          value: statusText,
          inline: true
        },
        {
          name: 'Processed',
          value: new Date().toLocaleString(),
          inline: true
        }
      ])
      .setTimestamp();

    // Add roles field only if roles were assigned
    if (processingResult.success && processingResult.assignedRoles.length > 0) {
      resultEmbed.addFields([
        {
          name: 'Roles Assigned',
          value: processingResult.assignedRoles.map(role => `• ${role.roleName}`).join('\n'),
          inline: false
        }
      ]);
    }

    // Add next steps field
    resultEmbed.addFields([
      {
        name: 'Next Steps',
        value: nextSteps,
        inline: false
      }
    ]);

    if (footer) {
      resultEmbed.setFooter({ text: footer });
    }

    // Send single final embed
    if (dmChannel) {
      await dmChannel.send({ embeds: [resultEmbed] });
    } else {
      // Fallback to interaction response
      await interaction.followUp({
        embeds: [resultEmbed],
        flags: MessageFlags.Ephemeral
      });
    }
  }

  // Enhanced logging to panels channel
  static async logEnhancedApplicationToPanel(interaction, sessionData, processingResult, config) {
    try {
      // Log to admin/panels channel
      const logChannelId = config.adminChannelId || config.logChannelId;
      if (!logChannelId) return;

      const logChannel = await interaction.guild.channels.fetch(logChannelId);
      if (!logChannel) return;

      const logEmbed = new EmbedBuilder()
        .setTitle('📋 Enhanced Application Processed')
        .setDescription(`**${sessionData.typeName || 'Application'}** completed by ${interaction.user.tag}`)
        .setColor(processingResult.success ? 0x00AE86 : 0xE74C3C)
        .addFields([
          {
            name: '👤 Applicant',
            value: `<@${interaction.user.id}> (${interaction.user.tag})`,
            inline: true
          },
          {
            name: '📊 Result',
            value: processingResult.success ? '✅ Approved' : '❌ Requires Review',
            inline: true
          },
          {
            name: '⏰ Processed',
            value: new Date().toLocaleString(),
            inline: true
          }
        ]);

      // Add responses
      const responsesText = Object.entries(sessionData.answers)
        .map(([index, answer]) => {
          const question = sessionData.questions[parseInt(index)];
          return `**Q${parseInt(index) + 1}:** ${question.question}\n**A:** ${answer}`;
        })
        .join('\n\n');

      if (responsesText.length <= 1024) {
        logEmbed.addFields([
          {
            name: '📝 Responses',
            value: responsesText,
            inline: false
          }
        ]);
      } else {
        // Split into multiple fields if too long
        const chunks = this.chunkText(responsesText, 1024);
        chunks.forEach((chunk, index) => {
          logEmbed.addFields([
            {
              name: index === 0 ? '📝 Responses' : '📝 Responses (continued)',
              value: chunk,
              inline: false
            }
          ]);
        });
      }

      // Add role assignments if any
      if (processingResult.assignedRoles.length > 0) {
        logEmbed.addFields([
          {
            name: '🎭 Roles Assigned',
            value: processingResult.assignedRoles.map(role => `• ${role.roleName}`).join('\n'),
            inline: false
          }
        ]);
      }

      // Add processing log
      if (processingResult.processingLog.length > 0) {
        const logText = processingResult.processingLog.join('\n');
        if (logText.length <= 1024) {
          logEmbed.addFields([
            {
              name: '🔄 Processing Log',
              value: logText,
              inline: false
            }
          ]);
        }
      }

      logEmbed.setFooter({ text: 'Enhanced Application System • Automated Processing' })
        .setTimestamp();

      await logChannel.send({ embeds: [logEmbed] });

    } catch (error) {
      console.error('Error logging enhanced application to panel:', error);
    }
  }

  // Enhanced error response
  static async sendEnhancedErrorResponse(interaction, sessionData) {
    const errorEmbed = new EmbedBuilder()
      .setTitle('⚠️ Processing Error')
      .setDescription('There was an error processing your application. Our staff has been notified.')
      .setColor(0xE74C3C)
      .addFields([
        {
          name: '📋 Status',
          value: 'Error - Manual Review Required',
          inline: true
        },
        {
          name: '⏰ Timestamp',
          value: new Date().toLocaleString(),
          inline: true
        },
        {
          name: '💬 Next Steps',
          value: 'Please contact staff for assistance. Your responses have been saved.',
          inline: false
        }
      ])
      .setFooter({ text: 'Professional Application System • Error Handling' });

    if (sessionData.useDM) {
      try {
        const dmChannel = await interaction.user.createDM();
        await dmChannel.send({ embeds: [errorEmbed] });
      } catch (dmError) {
        console.error('Could not send error response via DM:', dmError);
      }
    } else {
      try {
        await interaction.followUp({
          embeds: [errorEmbed],
          flags: MessageFlags.Ephemeral
        });
      } catch (interactionError) {
        console.error('Could not send error response via interaction:', interactionError);
      }
    }
  }

  // Utility method to chunk text
  static chunkText(text, maxLength) {
    const chunks = [];
    let currentChunk = '';
    const lines = text.split('\n');

    for (const line of lines) {
      if (currentChunk.length + line.length + 1 <= maxLength) {
        currentChunk += (currentChunk ? '\n' : '') + line;
      } else {
        if (currentChunk) {
          chunks.push(currentChunk);
          currentChunk = line;
        } else {
          // Line is too long, truncate it
          chunks.push(line.substring(0, maxLength - 3) + '...');
        }
      }
    }

    if (currentChunk) {
      chunks.push(currentChunk);
    }

    return chunks;
  }

  static getQueueSize() {
    try {
      const queueFile = path.join(__dirname, '..', 'application_queue.json');
      if (fs.existsSync(queueFile)) {
        const queue = JSON.parse(fs.readFileSync(queueFile, 'utf8'));
        // Count only queued items (not processed ones)
        return queue.filter(item => item.status === 'queued').length;
      }
    } catch (error) {
      console.error('Error getting queue size:', error);
    }
    return 0;
  }

  static async processApplicationImmediately(interaction, sessionData, config) {
    let responseSuccess = false;
    let assignedRoles = [];
    
    try {
      console.log('⚡ Processing application immediately - no queue delays');
      
      // Process verification silently in background
      const failedVerifications = [];
      for (const [index, result] of Object.entries(sessionData.verificationResults)) {
        const question = sessionData.questions[parseInt(index)];
        if (question.verifyInChannels && question.verificationMode === 'required' && !result.valid) {
          failedVerifications.push({
            question: question.question,
            answer: sessionData.answers[index],
            error: result.error
          });
        }
      }

      // Assign roles and log (if successful)
      if (failedVerifications.length === 0) {
        try {
          assignedRoles = await this.assignRoles(interaction, sessionData, config);
          await this.logCompletedForm(interaction, sessionData, assignedRoles, config);
        } catch (error) {
          console.error('Error in role assignment or logging:', error);
          // Continue to send response even if role assignment fails
        }
        
        // GUARANTEED RESPONSE: Send Form Submission embed (successful)
        await this.sendFormSubmissionEmbedWithRetry(interaction, sessionData);
        responseSuccess = true;
      } else {
        // GUARANTEED RESPONSE: Send Access Update embed (denied)
        await this.sendAccessUpdateEmbedWithRetry(interaction, sessionData, failedVerifications);
        responseSuccess = true;
      }
      
    } catch (error) {
      console.error('Critical error in processApplicationImmediately:', error);
      
      // FAIL-SAFE: If everything fails, send a basic response
      if (!responseSuccess) {
        await this.sendFailSafeResponse(interaction, sessionData);
      }
    } finally {
      // Always clear session - no queue to update since we processed immediately
      try {
        this.clearFormSession(sessionData.guildId, sessionData.userId);
        console.log('✅ Immediate processing complete - session cleared');
      } catch (error) {
        console.error('Error in cleanup:', error);
      }
    }
  }

  static async addToProcessingQueue(interaction, sessionData, config) {
    // Add application to queue
    const queueEntry = {
      guildId: sessionData.guildId,
      userId: sessionData.userId,
      username: sessionData.username,
      answers: sessionData.answers,
      verificationResults: sessionData.verificationResults,
      submittedAt: new Date().toISOString(),
      status: 'queued'
    };

    // Save to queue file
    this.saveToQueue(queueEntry);

    // Notify admins about new application in queue
    await this.notifyAdminsNewApplication(interaction, sessionData, config);
  }

  static saveToQueue(queueEntry) {
    try {
      const queueFile = path.join(__dirname, '..', 'application_queue.json');
      let queue = [];
      
      if (fs.existsSync(queueFile)) {
        queue = JSON.parse(fs.readFileSync(queueFile, 'utf8'));
      }
      
      queue.push(queueEntry);
      fs.writeFileSync(queueFile, JSON.stringify(queue, null, 2));
    } catch (error) {
      console.error('Error saving to queue:', error);
    }
  }

  static async notifyAdminsNewApplication(interaction, sessionData, config) {
    try {
      // Notify via admin channel if configured
      if (config.adminChannelId) {
        const adminChannel = await interaction.guild.channels.fetch(config.adminChannelId);
        if (adminChannel) {
          const notificationEmbed = new EmbedBuilder()
            .setDescription(`📥 New application received from ${sessionData.username}. Added to review queue.`)
            .setColor(0x3498DB)
            .setTimestamp();

          await adminChannel.send({ embeds: [notificationEmbed] });
        }
      }

      // Also notify via log channel if different from admin channel
      if (config.logChannelId && config.logChannelId !== config.adminChannelId) {
        const logChannel = await interaction.guild.channels.fetch(config.logChannelId);
        if (logChannel) {
          const logEmbed = new EmbedBuilder()
            .setDescription(`📥 New application received from ${sessionData.username}. Added to review queue.`)
            .setColor(0x3498DB)
            .setTimestamp();

          await logChannel.send({ embeds: [logEmbed] });
        }
      }
    } catch (error) {
      console.error('Error notifying admins:', error);
    }
  }

  static async processApplicationFromQueue(interaction, sessionData, config) {
    let responseSuccess = false;
    let assignedRoles = [];
    
    try {
      // Process verification silently in background
      const failedVerifications = [];
      for (const [index, result] of Object.entries(sessionData.verificationResults)) {
        const question = sessionData.questions[parseInt(index)];
        if (question.verifyInChannels && question.verificationMode === 'required' && !result.valid) {
          failedVerifications.push({
            question: question.question,
            answer: sessionData.answers[index],
            error: result.error
          });
        }
      }

      // Assign roles and log (if successful)
      if (failedVerifications.length === 0) {
        try {
          assignedRoles = await this.assignRoles(interaction, sessionData, config);
          await this.logCompletedForm(interaction, sessionData, assignedRoles, config);
        } catch (error) {
          console.error('Error in role assignment or logging:', error);
          // Continue to send response even if role assignment fails
        }
        
        // GUARANTEED RESPONSE: Send Form Submission embed (successful)
        await this.sendFormSubmissionEmbedWithRetry(interaction, sessionData);
        responseSuccess = true;
      } else {
        // GUARANTEED RESPONSE: Send Access Update embed (denied)
        await this.sendAccessUpdateEmbedWithRetry(interaction, sessionData, failedVerifications);
        responseSuccess = true;
      }
      
    } catch (error) {
      console.error('Critical error in processApplicationFromQueue:', error);
      
      // FAIL-SAFE: If everything fails, send a basic response
      if (!responseSuccess) {
        await this.sendFailSafeResponse(interaction, sessionData);
      }
    } finally {
      // Always update queue status and clear session
      try {
        this.updateQueueStatus(sessionData.guildId, sessionData.userId, assignedRoles.length > 0 ? 'approved' : 'processed');
        this.clearFormSession(sessionData.guildId, sessionData.userId);
      } catch (error) {
        console.error('Error in cleanup:', error);
      }
    }
  }

  static updateQueueStatus(guildId, userId, status) {
    try {
      const queueFile = path.join(__dirname, '..', 'application_queue.json');
      if (fs.existsSync(queueFile)) {
        let queue = JSON.parse(fs.readFileSync(queueFile, 'utf8'));
        
        // Find and update the application status
        const applicationIndex = queue.findIndex(app => app.guildId === guildId && app.userId === userId);
        if (applicationIndex !== -1) {
          queue[applicationIndex].status = status;
          queue[applicationIndex].processedAt = new Date().toISOString();
          fs.writeFileSync(queueFile, JSON.stringify(queue, null, 2));
        }
      }
    } catch (error) {
      console.error('Error updating queue status:', error);
    }
  }

  static async sendFailureMessage(interaction, sessionData, failedVerifications) {
    const failureMessage = '❌ Thank you for your application. Unfortunately, we cannot approve your request at this time. Please feel free to contact our staff if you have any questions.';

    if (sessionData.useDM) {
      const dmChannel = await interaction.user.createDM();
      await dmChannel.send({ content: failureMessage });
    } else {
      await interaction.followUp({
        content: failureMessage,
        flags: MessageFlags.Ephemeral
      });
    }
  }

  static async sendAnswerRecordedMessage(interaction, sessionData, isLastQuestion = false) {
    let embed;
    
    if (isLastQuestion) {
      embed = new EmbedBuilder()
        .setDescription('✅ Your application has been successfully submitted. Please wait while we review your responses.')
        .setColor(0x00AE86);
    } else {
      embed = new EmbedBuilder()
        .setDescription('✓ Answer recorded. Next question coming up...')
        .setColor(0x3498DB);
    }

    if (sessionData.useDM) {
      const dmChannel = await interaction.user.createDM();
      await dmChannel.send({ embeds: [embed] });
    } else {
      // For modal interactions, we need to handle this differently
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          embeds: [embed],
          flags: MessageFlags.Ephemeral
        });
      } else {
        await interaction.followUp({
          embeds: [embed],
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // No delay needed - proceed immediately to next question
  }

  static async sendAcknowledgmentMessage(interaction, sessionData) {
    const embed = new EmbedBuilder()
      .setDescription('📝 Thank you for completing the application! Your responses have been received and are being reviewed. You\'ll get a response shortly.')
      .setColor(0x00AE86);

    if (sessionData.useDM) {
      const dmChannel = await interaction.user.createDM();
      await dmChannel.send({ embeds: [embed] });
    } else {
      await interaction.followUp({
        embeds: [embed],
        flags: MessageFlags.Ephemeral
      });
    }

    // Process immediately - no artificial delays needed
  }

  static async sendFormSubmissionEmbed(interaction, sessionData) {
    const config = this.loadConfig(sessionData.guildId);
    
    // Get custom success message or use defaults
    const successMessage = config.messages?.success || {
      title: '✅ Your application has been accepted.',
      description: 'Thank you for completing the application process.',
      footer: null
    };

    // Replace placeholders
    const title = this.replacePlaceholders(successMessage.title, {
      username: interaction.user.displayName || interaction.user.username,
      form_data: this.formatFormData(sessionData)
    });
    
    const description = this.replacePlaceholders(successMessage.description, {
      username: interaction.user.displayName || interaction.user.username,
      form_data: this.formatFormData(sessionData)
    });

    const embed = new EmbedBuilder()
      .setTitle(title)
      .setColor(0x00AE86)
      .setDescription(description);

    if (successMessage.footer) {
      const footer = this.replacePlaceholders(successMessage.footer, {
        username: interaction.user.displayName || interaction.user.username,
        form_data: this.formatFormData(sessionData)
      });
      embed.setFooter({ text: footer });
    }

    if (sessionData.useDM) {
      const dmChannel = await interaction.user.createDM();
      await dmChannel.send({ embeds: [embed] });
    } else {
      // Handle different interaction states
      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({
            embeds: [embed],
            flags: MessageFlags.Ephemeral
          });
        } else if (interaction.deferred) {
          await interaction.editReply({
            embeds: [embed]
          });
        } else {
          await interaction.followUp({
            embeds: [embed],
            flags: MessageFlags.Ephemeral
          });
        }
      } catch (error) {
        console.error('Error sending form submission embed:', error);
        // Try alternative method
        try {
          const dmChannel = await interaction.user.createDM();
          await dmChannel.send({ embeds: [embed] });
        } catch (dmError) {
          console.error('Failed to send via DM as well:', dmError);
          throw error;
        }
      }
    }
  }

  static async sendFormSubmissionEmbedWithRetry(interaction, sessionData, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await this.sendFormSubmissionEmbed(interaction, sessionData);
        console.log(`✅ Form submission embed sent successfully on attempt ${attempt}`);
        return; // Success, exit retry loop
      } catch (error) {
        console.error(`❌ Attempt ${attempt} failed to send form submission embed:`, error);
        
        if (attempt === maxRetries) {
          // Final attempt failed, send fail-safe response
          console.error('🚨 All attempts failed, sending fail-safe response');
          await this.sendFailSafeResponse(interaction, sessionData);
        } else {
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }
  }

  static async sendAccessUpdateEmbed(interaction, sessionData, failedVerifications) {
    const config = this.loadConfig(sessionData.guildId);
    
    // Get custom denial message or use defaults
    const denialMessage = config.messages?.denial || {
      title: '❌ Verification failed. Please try again or contact staff for help.',
      description: 'Your application could not be processed at this time.',
      footer: null
    };

    // Prepare reason for denial
    const reason = failedVerifications.length > 0 
      ? 'Background verification could not be completed'
      : 'Application requirements not met';

    // Replace placeholders
    const title = this.replacePlaceholders(denialMessage.title, {
      username: interaction.user.displayName || interaction.user.username,
      reason: reason,
      form_data: this.formatFormData(sessionData)
    });
    
    const description = this.replacePlaceholders(denialMessage.description, {
      username: interaction.user.displayName || interaction.user.username,
      reason: reason,
      form_data: this.formatFormData(sessionData)
    });

    const embed = new EmbedBuilder()
      .setTitle(title)
      .setColor(0xE74C3C)
      .setDescription(description);

    if (denialMessage.footer) {
      const footer = this.replacePlaceholders(denialMessage.footer, {
        username: interaction.user.displayName || interaction.user.username,
        reason: reason,
        form_data: this.formatFormData(sessionData)
      });
      embed.setFooter({ text: footer });
    }

    if (sessionData.useDM) {
      const dmChannel = await interaction.user.createDM();
      await dmChannel.send({ embeds: [embed] });
    } else {
      // Handle different interaction states
      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({
            embeds: [embed],
            flags: MessageFlags.Ephemeral
          });
        } else if (interaction.deferred) {
          await interaction.editReply({
            embeds: [embed]
          });
        } else {
          await interaction.followUp({
            embeds: [embed],
            flags: MessageFlags.Ephemeral
          });
        }
      } catch (error) {
        console.error('Error sending access update embed:', error);
        // Try alternative method
        try {
          const dmChannel = await interaction.user.createDM();
          await dmChannel.send({ embeds: [embed] });
        } catch (dmError) {
          console.error('Failed to send via DM as well:', dmError);
          throw error;
        }
      }
    }
  }

  static async sendAccessUpdateEmbedWithRetry(interaction, sessionData, failedVerifications, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await this.sendAccessUpdateEmbed(interaction, sessionData, failedVerifications);
        console.log(`✅ Access update embed sent successfully on attempt ${attempt}`);
        return; // Success, exit retry loop
      } catch (error) {
        console.error(`❌ Attempt ${attempt} failed to send access update embed:`, error);
        
        if (attempt === maxRetries) {
          // Final attempt failed, send fail-safe response
          console.error('🚨 All attempts failed, sending fail-safe response');
          await this.sendFailSafeResponse(interaction, sessionData);
        } else {
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }
  }

  static async sendFailSafeResponse(interaction, sessionData) {
    console.log('🚨 FAIL-SAFE: Sending guaranteed response to user');
    
    const failSafeEmbed = new EmbedBuilder()
      .setTitle('📄 Application Received')
      .setDescription('Your application has been received and is being processed. You will be contacted if additional information is needed.')
      .setColor(0x3498DB);

    try {
      if (sessionData.useDM) {
        const dmChannel = await interaction.user.createDM();
        await dmChannel.send({ embeds: [failSafeEmbed] });
        console.log('✅ Fail-safe response sent via DM');
      } else {
        // Handle different interaction states for fail-safe
        try {
          if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({
              embeds: [failSafeEmbed],
              flags: MessageFlags.Ephemeral
            });
          } else if (interaction.deferred) {
            await interaction.editReply({
              embeds: [failSafeEmbed]
            });
          } else {
            await interaction.followUp({
              embeds: [failSafeEmbed],
              flags: MessageFlags.Ephemeral
            });
          }
          console.log('✅ Fail-safe response sent via interaction');
        } catch (interactionError) {
          console.error('Error with interaction response, trying DM:', interactionError);
          // Try DM as fallback
          const dmChannel = await interaction.user.createDM();
          await dmChannel.send({ embeds: [failSafeEmbed] });
        }
      }
    } catch (error) {
      console.error('🚨 CRITICAL: Even fail-safe response failed:', error);
      
      // Last resort: try basic text message
      try {
        const basicMessage = 'Your application has been received and is being processed.';
        
        if (sessionData.useDM) {
          const dmChannel = await interaction.user.createDM();
          await dmChannel.send({ content: basicMessage });
        } else {
          await interaction.followUp({
            content: basicMessage,
            flags: MessageFlags.Ephemeral
          });
        }
        console.log('✅ Last resort basic message sent');
      } catch (finalError) {
        console.error('🚨 ABSOLUTE FAILURE: Could not send any response to user:', finalError);
        // Log this critical failure for manual intervention
        this.logCriticalFailure(sessionData, finalError);
      }
    }
  }

  static logCriticalFailure(sessionData, error) {
    try {
      const failureLog = {
        timestamp: new Date().toISOString(),
        userId: sessionData.userId,
        username: sessionData.username,
        guildId: sessionData.guildId,
        error: error.message,
        stack: error.stack,
        sessionData: sessionData
      };
      
      const logFile = path.join(__dirname, '..', 'critical_failures.json');
      let failures = [];
      
      if (fs.existsSync(logFile)) {
        failures = JSON.parse(fs.readFileSync(logFile, 'utf8'));
      }
      
      failures.push(failureLog);
      fs.writeFileSync(logFile, JSON.stringify(failures, null, 2));
      
      console.error('🚨 Critical failure logged to critical_failures.json');
    } catch (logError) {
      console.error('🚨 Could not even log the critical failure:', logError);
    }
  }

  static async sendUserSummary(interaction, sessionData, assignedRoles, approved) {
    const embed = new EmbedBuilder()
      .setTitle('📋 Application Summary')
      .setDescription('Here\'s a summary of your application:')
      .setColor(approved ? 0x00AE86 : 0xE74C3C)
      .addFields([
        {
          name: '📝 Your Responses',
          value: Object.entries(sessionData.answers)
            .map(([index, answer]) => {
              const question = sessionData.questions[parseInt(index)];
              return `**${question.question}**\n${answer}`;
            })
            .join('\n\n'),
          inline: false
        },
        {
          name: '📊 Application Status',
          value: approved ? '✅ Approved' : '❌ Not Approved',
          inline: true
        },
        {
          name: '⏰ Processed',
          value: new Date().toLocaleString(),
          inline: true
        }
      ]);

    if (approved && assignedRoles.length > 0) {
      embed.addFields([
        {
          name: '🎭 Roles Assigned',
          value: assignedRoles.map(roleId => `<@&${roleId}>`).join(', '),
          inline: false
        }
      ]);
    }

    embed.setFooter({ 
      text: approved 
        ? 'Welcome to the community! If you have any questions, feel free to ask our staff.' 
        : 'If you have questions about this decision, please contact our staff team.'
    });

    if (sessionData.useDM) {
      const dmChannel = await interaction.user.createDM();
      await dmChannel.send({ embeds: [embed] });
    } else {
      await interaction.followUp({
        embeds: [embed],
        flags: MessageFlags.Ephemeral
      });
    }
  }

  static async sendSuccessMessage(interaction, sessionData, assignedRoles) {
    // This method is now replaced by sendDecisionMessage and sendUserSummary
    // Keeping for backward compatibility if needed elsewhere
    const successMessage = '✅ Great news! Your application has been approved. Welcome to our community!';

    if (sessionData.useDM) {
      const dmChannel = await interaction.user.createDM();
      await dmChannel.send({ content: successMessage });
    } else {
      await interaction.followUp({
        content: successMessage,
        flags: MessageFlags.Ephemeral
      });
    }
  }

  static async assignRoles(interaction, sessionData, config) {
    const assignedRoles = [];
    
    try {
      const guild = interaction.guild;
      const member = await guild.members.fetch(sessionData.userId);
      
      for (const [questionIndex, answer] of Object.entries(sessionData.answers)) {
        // Get role assignments from the correct location (multi-type or legacy)
        let roleAssignments = null;
        if (sessionData.typeId && config.applicationTypes && config.applicationTypes[sessionData.typeId]) {
          // New multi-type system
          roleAssignments = config.applicationTypes[sessionData.typeId].roleAssignments?.[questionIndex];
        } else {
          // Legacy system
          roleAssignments = config.roleAssignments?.[questionIndex];
        }
        
        if (roleAssignments) {
          let roleId = null;

          // First try exact match (for choice questions and exact text matches)
          if (roleAssignments[answer]) {
            roleId = roleAssignments[answer];
          } else if (roleAssignments['__text_answer__']) {
            // For simple text questions, assign role for any answer
            roleId = roleAssignments['__text_answer__'];
          } else {
            // For advanced text questions, try case-insensitive matching
            const normalizedAnswer = answer.toLowerCase().trim();
            roleId = roleAssignments[normalizedAnswer];
          }

          if (roleId) {
            try {
              const role = await guild.roles.fetch(roleId);
              if (role) {
                await member.roles.add(role, 'Automatic role assignment from verified dynamic form');
                assignedRoles.push(roleId);
                console.log(`Assigned role ${role.name} to user for answer: "${answer}"`);
              }
            } catch (error) {
              console.error(`Error assigning role ${roleId}:`, error);
            }
          }
        }
      }
    } catch (error) {
      console.error('Error in role assignment:', error);
    }
    
    return assignedRoles;
  }

  static async logCompletedForm(interaction, sessionData, assignedRoles, config) {
    if (!config.logChannelId) return;

    try {
      const logChannel = await interaction.guild.channels.fetch(config.logChannelId);
      if (!logChannel) return;

      const logEmbed = new EmbedBuilder()
        .setTitle('📋 Dynamic Form Submission')
        .setDescription('New application completed with real-time verification')
        .setColor(0x00AE86)
        .addFields([
          {
            name: '👤 User Information',
            value: `<@${sessionData.userId}> (${sessionData.username})\nID: ${sessionData.userId}`,
            inline: true
          },
          {
            name: '⏰ Submission Details',
            value: `Started: ${new Date(sessionData.startTime).toLocaleString()}\nCompleted: ${new Date().toLocaleString()}\nMethod: ${sessionData.useDM ? 'Direct Message' : 'Server Modal'}`,
            inline: true
          },
          {
            name: '🎭 Roles Assigned',
            value: assignedRoles.length > 0 
              ? assignedRoles.map(roleId => `<@&${roleId}>`).join(', ')
              : 'None',
            inline: false
          }
        ])
        .setTimestamp()
        .setFooter({ text: `Form ID: ${sessionData.guildId}-${sessionData.userId}` });

      // Add questions and answers
      const questionsAndAnswers = Object.entries(sessionData.answers)
        .map(([index, answer]) => {
          const question = sessionData.questions[parseInt(index)];
          const verification = sessionData.verificationResults[index];
          const verificationIcon = verification?.valid ? '✅' : '⚠️';
          return `**Q${parseInt(index) + 1}:** ${question.question}\n**A:** ${answer} ${verificationIcon}`;
        })
        .join('\n\n');

      if (questionsAndAnswers.length > 1024) {
        // Split into multiple fields if too long
        const chunks = this.chunkString(questionsAndAnswers, 1024);
        chunks.forEach((chunk, index) => {
          logEmbed.addFields([
            {
              name: `📝 Questions & Answers ${index > 0 ? `(Part ${index + 1})` : ''}`,
              value: chunk,
              inline: false
            }
          ]);
        });
      } else {
        logEmbed.addFields([
          {
            name: '📝 Questions & Answers',
            value: questionsAndAnswers,
            inline: false
          }
        ]);
      }

      // Add verification summary
      const verificationSummary = Object.entries(sessionData.verificationResults)
        .map(([index, result]) => {
          const question = sessionData.questions[parseInt(index)];
          if (question.verifyInChannels) {
            return `Q${parseInt(index) + 1}: ${result.valid ? '✅ Verified' : '⚠️ Warning'}`;
          }
          return `Q${parseInt(index) + 1}: No verification`;
        })
        .join('\n');

      logEmbed.addFields([
        {
          name: '🔍 Verification Summary',
          value: verificationSummary,
          inline: false
        }
      ]);

      // Add revoke button
      const revokeButton = new ActionRowBuilder()
        .addComponents(
          new ButtonBuilder()
            .setCustomId(`revoke_application_${sessionData.guildId}_${sessionData.userId}`)
            .setLabel('Revoke Access')
            .setStyle(ButtonStyle.Danger)
            .setEmoji('🚫')
        );

      const logMessage = await logChannel.send({
        embeds: [logEmbed],
        components: [revokeButton]
      });

      // Store log message ID for future reference
      sessionData.logMessageId = logMessage.id;
      
    } catch (error) {
      console.error('Error logging completed form:', error);
    }
  }

  static chunkString(str, length) {
    const chunks = [];
    let index = 0;
    while (index < str.length) {
      chunks.push(str.slice(index, index + length));
      index += length;
    }
    return chunks;
  }

  static async handleRevoke(interaction) {
    const [, , guildId, userId] = interaction.customId.split('_');
    
    const modal = new ModalBuilder()
      .setCustomId(`revoke_modal_${guildId}_${userId}`)
      .setTitle('Revoke User Access');

    const reasonInput = new TextInputBuilder()
      .setCustomId('reason')
      .setLabel('Revocation Reason')
      .setStyle(TextInputStyle.Paragraph)
      .setRequired(true)
      .setMaxLength(500)
      .setPlaceholder('Enter the reason for revoking this user\'s access...');

    const actionRow = new ActionRowBuilder().addComponents(reasonInput);
    modal.addComponents(actionRow);

    await interaction.showModal(modal);
  }

  static async processRevoke(interaction) {
    const [, , guildId, userId] = interaction.customId.split('_');
    const reason = interaction.fields.getTextInputValue('reason');
    
    try {
      const guild = interaction.guild;
      const member = await guild.members.fetch(userId);
      const config = this.loadConfig(guildId);
      
      // Remove all roles that were assigned through the application system
      const rolesToRemove = [];
      for (const roleAssignments of Object.values(config.roleAssignments || {})) {
        for (const roleId of Object.values(roleAssignments)) {
          if (member.roles.cache.has(roleId)) {
            rolesToRemove.push(roleId);
          }
        }
      }

      // Remove roles
      for (const roleId of rolesToRemove) {
        try {
          const role = await guild.roles.fetch(roleId);
          if (role) {
            await member.roles.remove(role, `Access revoked by ${interaction.user.tag}: ${reason}`);
          }
        } catch (error) {
          console.error(`Error removing role ${roleId}:`, error);
        }
      }

      // Update the log embed
      const originalEmbed = interaction.message.embeds[0];
      const updatedEmbed = new EmbedBuilder(originalEmbed.toJSON())
        .setColor(0xE74C3C)
        .setTitle('🚫 Dynamic Form Submission - REVOKED')
        .addFields([
          {
            name: '🚫 Revocation Details',
            value: `**Revoked by:** <@${interaction.user.id}> (${interaction.user.tag})\n**Reason:** ${reason}\n**Revoked at:** ${new Date().toLocaleString()}`,
            inline: false
          },
          {
            name: '🎭 Roles Removed',
            value: rolesToRemove.length > 0 
              ? rolesToRemove.map(roleId => `<@&${roleId}>`).join(', ')
              : 'None',
            inline: false
          }
        ]);

      await interaction.update({
        embeds: [updatedEmbed],
        components: [] // Remove the revoke button
      });

      // Notify the user if possible
      try {
        const dmChannel = await member.user.createDM();
        
        // Get custom revoke message or use defaults
        const revokeMessage = config.messages?.revoke || {
          title: '🚫 Access Revoked',
          description: 'Your access to {server_name} has been revoked.',
          footer: 'If you believe this was done in error, please contact the server staff.'
        };

        // Replace placeholders
        const title = this.replacePlaceholders(revokeMessage.title, {
          username: member.user.displayName || member.user.username,
          server_name: guild.name,
          reason: reason
        });
        
        const description = this.replacePlaceholders(revokeMessage.description, {
          username: member.user.displayName || member.user.username,
          server_name: guild.name,
          reason: reason
        });

        const notificationEmbed = new EmbedBuilder()
          .setTitle(title)
          .setDescription(description)
          .setColor(0xE74C3C)
          .addFields([
            {
              name: '📝 Reason',
              value: reason,
              inline: false
            }
          ])
          .setTimestamp();

        if (revokeMessage.footer) {
          const footer = this.replacePlaceholders(revokeMessage.footer, {
            username: member.user.displayName || member.user.username,
            server_name: guild.name,
            reason: reason
          });
          notificationEmbed.setFooter({ text: footer });
        }

        await dmChannel.send({ embeds: [notificationEmbed] });
      } catch (error) {
        console.log('Could not send DM notification to revoked user');
      }

    } catch (error) {
      console.error('Error processing revoke:', error);
      await interaction.reply({
        content: '❌ An error occurred while processing the revocation.',
        flags: MessageFlags.Ephemeral
      });
    }
  }

  static replacePlaceholders(text, placeholders) {
    if (!text) return text;
    
    let result = text;
    Object.entries(placeholders).forEach(([key, value]) => {
      const placeholder = `{${key}}`;
      result = result.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), value || '');
    });
    
    return result;
  }

  static formatFormData(sessionData) {
    if (!sessionData.answers || !sessionData.questions) return 'No form data available';
    
    return Object.entries(sessionData.answers)
      .map(([index, answer]) => {
        const questionIndex = parseInt(index);
        if (isNaN(questionIndex) || questionIndex < 0 || questionIndex >= sessionData.questions.length) {
          return `**Question ${index}**\n${answer}`;
        }
        const question = sessionData.questions[questionIndex];
        return `**${question ? question.question : `Question ${questionIndex + 1}`}**\n${answer}`;
      })
      .join('\n\n');
  }
}

module.exports = DynamicFormHandler;