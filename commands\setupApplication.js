const {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  ModalBuilder,
  TextInputBuilder,
  TextInputStyle,
  StringSelectMenuBuilder,
  PermissionFlagsBits,
  MessageFlags
} = require('discord.js');
const fs = require('fs');
const path = require('path');

// Application storage file
const APPLICATION_CONFIG_FILE = path.join(__dirname, '..', 'application_config.json');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('setup-application')
    .setDescription('Set up the application system for your server')
    .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

  async execute(interaction) {
    try {
      await this.showMainSetup(interaction);
    } catch (error) {
      console.error('Error in setup-application command:', error);
      await interaction.reply({
        content: 'An error occurred while setting up the application system.',
        flags: MessageFlags.Ephemeral
      });
    }
  },

  async showMainSetup(interaction, isUpdate = false) {
    const config = this.loadConfig(interaction.guildId);

    // Count total questions across all application types
    const totalQuestions = config.applicationTypes ? 
      Object.values(config.applicationTypes).reduce((sum, type) => sum + (type.questions?.length || 0), 0) : 
      config.questions?.length || 0;

    // Count application types
    const typeCount = config.applicationTypes ? Object.keys(config.applicationTypes).length : 0;

    const embed = new EmbedBuilder()
      .setTitle('🛠️ Application System Setup')
      .setDescription('Configure your server\'s application system with custom questions and automatic role assignment.')
      .setColor(0x00AE86)
      .addFields([
        {
          name: '📋 Current Status',
          value: config.enabled ? '✅ Enabled' : '❌ Disabled',
          inline: true
        },
        {
          name: '📝 Application Types',
          value: `${typeCount} configured`,
          inline: true
        },
        {
          name: '❓ Total Questions',
          value: `${totalQuestions} questions`,
          inline: true
        },
        {
          name: '📝 Application Channel',
          value: config.applicationChannelId ? `<#${config.applicationChannelId}>` : 'Not set',
          inline: true
        },
        {
          name: '📊 Log Channel',
          value: config.logChannelId ? `<#${config.logChannelId}>` : 'Not set',
          inline: true
        },
        {
          name: '👑 Admin Channel',
          value: config.adminChannelId ? `<#${config.adminChannelId}>` : 'Not set',
          inline: true
        }
      ]);

    const buttons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_create_new_application')
          .setLabel('Create New Application')
          .setStyle(ButtonStyle.Success)
          .setEmoji('➕'),
        new ButtonBuilder()
          .setCustomId('app_manage_applications')
          .setLabel('Manage Applications')
          .setStyle(ButtonStyle.Primary)
          .setEmoji('📋'),
        new ButtonBuilder()
          .setCustomId('app_setup_channels')
          .setLabel('Set Channels')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('📝')
      );

    const messageButtons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_setup_messages')
          .setLabel('🛠️ Message Configuration')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('💬')
      );

    const controlButtons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_toggle_system')
          .setLabel(config.enabled ? 'Disable System' : 'Enable System')
          .setStyle(config.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
          .setEmoji(config.enabled ? '❌' : '✅'),
        new ButtonBuilder()
          .setCustomId('app_deploy_panel')
          .setLabel('Deploy Panel')
          .setStyle(ButtonStyle.Success)
          .setEmoji('🚀')
          .setDisabled(!config.enabled || (!config.applicationTypes || Object.keys(config.applicationTypes).length === 0)),
        new ButtonBuilder()
          .setCustomId('app_test_system')
          .setLabel('Test Application')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('🧪')
          .setDisabled(!config.enabled)
      );

    const panelButtons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_recreate_panel')
          .setLabel('Recreate Panel')
          .setStyle(ButtonStyle.Primary)
          .setEmoji('🔄')
          .setDisabled(!config.enabled || totalQuestions === 0 || !config.applicationPanelMessageId),
        new ButtonBuilder()
          .setCustomId('app_remove_application')
          .setLabel('Remove Panel')
          .setStyle(ButtonStyle.Danger)
          .setEmoji('🗑️')
          .setDisabled(!config.applicationPanelMessageId),
        new ButtonBuilder()
          .setCustomId('app_clear_all_data')
          .setLabel('Clear All Data')
          .setStyle(ButtonStyle.Danger)
          .setEmoji('💥')
      );

    if (isUpdate) {
      await interaction.update({
        embeds: [embed],
        components: [buttons, messageButtons, controlButtons, panelButtons]
      });
    } else {
      await interaction.reply({
        embeds: [embed],
        components: [buttons, messageButtons, controlButtons, panelButtons],
        flags: MessageFlags.Ephemeral
      });
    }
  },

  async handleButtonInteraction(interaction) {
    const customId = interaction.customId;

    try {
      switch (customId) {
        case 'app_create_new_application':
          await this.showCreateNewApplicationModal(interaction);
          break;
        case 'app_manage_applications':
          await this.showManageApplications(interaction);
          break;
        case 'app_setup_questions':
          await this.showQuestionsManager(interaction);
          break;
        case 'app_add_question':
          await this.showAddQuestionModal(interaction);
          break;
        case 'app_setup_channels':
          await this.showChannelSetup(interaction);
          break;
        case 'app_setup_roles':
          await this.showRoleAssignmentSetup(interaction);
          break;
        case 'app_setup_messages':
          await this.showMessageConfiguration(interaction);
          break;
        case 'app_toggle_system':
          await this.toggleSystem(interaction);
          break;
        case 'app_deploy_panel':
          await this.deployMultiTypeApplicationPanel(interaction);
          break;
        case 'app_test_system':
          await this.testApplication(interaction);
          break;
        case 'app_remove_application':
          await this.removeApplicationPanel(interaction);
          break;
        case 'app_recreate_panel':
          await this.recreateApplicationPanel(interaction);
          break;
        case 'app_clear_all_data':
          await this.showClearDataConfirmation(interaction);
          break;
        case 'app_edit_type':
          await this.showEditApplicationType(interaction);
          break;
        case 'app_delete_type':
          await this.showDeleteApplicationType(interaction);
          break;
        case 'app_set_default_type':
          await this.showSetDefaultType(interaction);
          break;
        case 'app_migrate_legacy':
          await this.migrateLegacyQuestions(interaction);
          break;
        case 'app_back_main':
          await this.showMainSetup(interaction, true);
          break;
        case 'app_edit_success_message':
          await this.showEditMessageModal(interaction, 'success');
          break;
        case 'app_edit_denial_message':
          await this.showEditMessageModal(interaction, 'denial');
          break;
        case 'app_edit_submission_message':
          await this.showEditMessageModal(interaction, 'submission');
          break;
        case 'app_edit_revoke_message':
          await this.showEditMessageModal(interaction, 'revoke');
          break;
        case 'app_preview_success':
          await this.previewMessage(interaction, 'success');
          break;
        case 'app_preview_denial':
          await this.previewMessage(interaction, 'denial');
          break;
        case 'app_preview_revoke':
          await this.previewMessage(interaction, 'revoke');
          break;
        case 'app_reset_messages':
          await this.resetMessages(interaction);
          break;
        case 'app_confirm_reset_messages':
          await this.confirmResetMessages(interaction);
          break;
        case 'app_cancel_reset_messages':
          await this.showMessageConfiguration(interaction);
          break;
        default:
          // Handle specific buttons with dynamic IDs
          if (customId.startsWith('app_')) {
            await this.handleSpecificButton(interaction);
          }
          break;
      }
    } catch (error) {
      console.error('Error handling application button:', error);
      
      // Check if the interaction has expired
      if (error.code === 10062 || error.message.includes('Unknown interaction')) {
        console.log('Interaction expired - cannot respond');
        return;
      }
      
      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({
            content: 'An error occurred while processing your request.',
            flags: MessageFlags.Ephemeral
          });
        } else {
          await interaction.followUp({
            content: 'An error occurred while processing your request.',
            flags: MessageFlags.Ephemeral
          });
        }
      } catch (responseError) {
        console.error('Could not respond to interaction:', responseError);
      }
    }
  },

  loadConfig(guildId) {
    try {
      if (fs.existsSync(APPLICATION_CONFIG_FILE)) {
        const data = JSON.parse(fs.readFileSync(APPLICATION_CONFIG_FILE, 'utf8'));
        return data[guildId] || this.getDefaultConfig();
      }
    } catch (error) {
      console.error('Error loading application config:', error);
    }
    return this.getDefaultConfig();
  },

  saveConfig(guildId, config) {
    try {
      let data = {};
      if (fs.existsSync(APPLICATION_CONFIG_FILE)) {
        data = JSON.parse(fs.readFileSync(APPLICATION_CONFIG_FILE, 'utf8'));
      }
      data[guildId] = config;
      fs.writeFileSync(APPLICATION_CONFIG_FILE, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('Error saving application config:', error);
    }
  },

  async showQuestionsManager(interaction) {
    const config = this.loadConfig(interaction.guildId);

    // Auto-migrate to new system if needed
    if (!config.applicationTypes && config.questions && config.questions.length > 0) {
      this.autoMigrateToMultiType(config, interaction.guildId);
    }

    // Get all questions from all application types
    const allQuestions = this.getAllQuestions(config);

    const embed = new EmbedBuilder()
      .setTitle('❓ Questions Manager')
      .setDescription('Manage your application questions here. **To create multiple application types**, specify different names in the "Application Type" field when adding questions.\n\n💡 **Tip:** Leave "Application Type" blank for general questions, or specify "Staff Application", "Whitelist", etc. to create those types automatically!')
      .setColor(0x3498DB);

    if (allQuestions.length === 0) {
      embed.addFields([
        {
          name: '📝 No Questions',
          value: 'No questions have been configured yet. Click "Add Question" to get started.',
          inline: false
        }
      ]);
    } else {
      // Group questions by application type
      const questionsByType = this.groupQuestionsByType(config);
      
      let questionsList = '';
      Object.entries(questionsByType).forEach(([typeName, questions]) => {
        questionsList += `**${typeName}:**\n`;
        questions.forEach((q, i) => {
          questionsList += `  ${i + 1}. ${q.question} (${q.type}${q.required ? ', required' : ', optional'})\n`;
        });
        questionsList += '\n';
      });

      embed.addFields([
        {
          name: '📋 Current Questions',
          value: questionsList || 'No questions configured',
          inline: false
        }
      ]);
    }

    const buttons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_add_question')
          .setLabel('Add Question')
          .setStyle(ButtonStyle.Success)
          .setEmoji('➕'),
        new ButtonBuilder()
          .setCustomId('app_edit_question')
          .setLabel('Edit Question')
          .setStyle(ButtonStyle.Primary)
          .setEmoji('✏️')
          .setDisabled(allQuestions.length === 0),
        new ButtonBuilder()
          .setCustomId('app_delete_question')
          .setLabel('Delete Question')
          .setStyle(ButtonStyle.Danger)
          .setEmoji('🗑️')
          .setDisabled(allQuestions.length === 0)
      );

    const backButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_back_main')
          .setLabel('Back to Main')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️')
      );

    await interaction.update({
      embeds: [embed],
      components: [buttons, backButton]
    });
  },

  async showChannelSetup(interaction) {
    const config = this.loadConfig(interaction.guildId);

    const embed = new EmbedBuilder()
      .setTitle('📝 Channel Configuration')
      .setDescription('Set up the channels for your application system.')
      .setColor(0x9B59B6)
      .addFields([
        {
          name: '📋 Application Channel',
          value: config.applicationChannelId ? `<#${config.applicationChannelId}>` : 'Not set',
          inline: true
        },
        {
          name: '📊 Logging Channel',
          value: config.logChannelId ? `<#${config.logChannelId}>` : 'Not set',
          inline: true
        },
        {
          name: '🔍 Verification Channels',
          value: config.verificationChannelIds && config.verificationChannelIds.length > 0 
            ? config.verificationChannelIds.map(id => `<#${id}>`).join(', ')
            : 'All accessible channels',
          inline: false
        }
      ]);

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId('app_select_channel_type')
      .setPlaceholder('Select channel type to configure...')
      .addOptions([
        {
          label: 'Application Channel',
          value: 'application',
          description: 'Where users will see the application panel',
          emoji: '📋'
        },
        {
          label: 'Logging Channel',
          value: 'log',
          description: 'Where submissions and admin notifications are sent',
          emoji: '📊'
        },
        {
          label: 'Verification Channels',
          value: 'verification',
          description: 'Channels where bot will search for user data',
          emoji: '🔍'
        }
      ]);

    const selectRow = new ActionRowBuilder().addComponents(selectMenu);

    const backButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_back_main')
          .setLabel('Back to Main')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️')
      );

    await interaction.update({
      embeds: [embed],
      components: [selectRow, backButton]
    });
  },

  async showRoleAssignmentSetup(interaction) {
    const config = this.loadConfig(interaction.guildId);

    const embed = new EmbedBuilder()
      .setTitle('🎭 Role Assignment Configuration')
      .setDescription('Configure automatic role assignment based on user answers.')
      .setColor(0xE67E22);

    if (config.questions.length === 0) {
      embed.addFields([
        {
          name: '❌ No Questions',
          value: 'You need to add questions first before configuring role assignments.',
          inline: false
        }
      ]);
    } else {
      const roleAssignmentInfo = config.questions.map((q, i) => {
        const assignments = config.roleAssignments[i] || {};
        const assignmentCount = Object.keys(assignments).length;
        return `**${i + 1}.** ${q.question}\n└ ${assignmentCount} role assignments configured`;
      }).join('\n\n');

      embed.addFields([
        {
          name: '📋 Questions & Role Assignments',
          value: roleAssignmentInfo,
          inline: false
        }
      ]);
    }

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId('app_select_question_roles')
      .setPlaceholder('Select question to configure roles...')
      .setDisabled(config.questions.length === 0);

    if (config.questions.length > 0) {
      selectMenu.addOptions(
        config.questions.map((q, i) => ({
          label: `Question ${i + 1}: ${q.question.substring(0, 50)}${q.question.length > 50 ? '...' : ''}`,
          value: i.toString(),
          description: `Configure roles for this question`,
          emoji: '🎭'
        }))
      );
    }

    const selectRow = new ActionRowBuilder().addComponents(selectMenu);

    const backButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_back_main')
          .setLabel('Back to Main')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️')
      );

    await interaction.update({
      embeds: [embed],
      components: [selectRow, backButton]
    });
  },

  async showMessageConfiguration(interaction) {
    const config = this.loadConfig(interaction.guildId);
    
    // Ensure messages object exists with defaults
    if (!config.messages) {
      const defaultConfig = this.getDefaultConfig();
      config.messages = defaultConfig.messages;
      this.saveConfig(interaction.guildId, config);
    }

    // Ensure all message types exist
    const defaultMessages = this.getDefaultConfig().messages;
    if (!config.messages.success) config.messages.success = defaultMessages.success;
    if (!config.messages.denial) config.messages.denial = defaultMessages.denial;
    if (!config.messages.submission) config.messages.submission = defaultMessages.submission;
    if (!config.messages.revoke) config.messages.revoke = defaultMessages.revoke;

    const embed = new EmbedBuilder()
      .setTitle('💬 Message Configuration')
      .setDescription('Customize the messages your bot sends to users during the application process.')
      .setColor(0x9B59B6)
      .addFields([
        {
          name: '✅ Success Message',
          value: `**Title:** ${config.messages.success.title}\n**Description:** ${config.messages.success.description}`,
          inline: false
        },
        {
          name: '🚫 Denial Message',
          value: `**Title:** ${config.messages.denial.title}\n**Description:** ${config.messages.denial.description}`,
          inline: false
        },
        {
          name: '📄 Submission Summary',
          value: `**Title:** ${config.messages.submission.title}\n**Description:** ${config.messages.submission.description}`,
          inline: false
        },
        {
          name: '🚫 Revoke Message',
          value: `**Title:** ${config.messages.revoke.title}\n**Description:** ${config.messages.revoke.description}`,
          inline: false
        },
        {
          name: '⏳ Pending Review Message',
          value: `**Title:** ${config.messages.pending.title}\n**Description:** ${config.messages.pending.description}`,
          inline: false
        }
      ])
      .setFooter({ text: 'Click a button below to edit each message type' });

    const messageButtons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_edit_success_message')
          .setLabel('Edit Approval Message')
          .setStyle(ButtonStyle.Success)
          .setEmoji('✅'),
        new ButtonBuilder()
          .setCustomId('app_edit_denial_message')
          .setLabel('Edit Denial Message')
          .setStyle(ButtonStyle.Danger)
          .setEmoji('🚫'),
        new ButtonBuilder()
          .setCustomId('app_edit_submission_message')
          .setLabel('Edit Form Submission Message')
          .setStyle(ButtonStyle.Primary)
          .setEmoji('📄')
      );

    const revokeButtons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_edit_revoke_message')
          .setLabel('Edit Revoke Message')
          .setStyle(ButtonStyle.Danger)
          .setEmoji('🚫'),
        new ButtonBuilder()
          .setCustomId('app_preview_revoke')
          .setLabel('Preview Revoke')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('👁️')
      );

    const previewButtons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_preview_success')
          .setLabel('Preview Success')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('👁️'),
        new ButtonBuilder()
          .setCustomId('app_preview_denial')
          .setLabel('Preview Denial')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('👁️'),
        new ButtonBuilder()
          .setCustomId('app_reset_messages')
          .setLabel('Reset to Defaults')
          .setStyle(ButtonStyle.Danger)
          .setEmoji('🔄')
      );

    const backButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_back_main')
          .setLabel('Back to Main')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️')
      );

    await interaction.update({
      embeds: [embed],
      components: [messageButtons, revokeButtons, previewButtons, backButton]
    });
  },

  async toggleSystem(interaction) {
    const config = this.loadConfig(interaction.guildId);
    config.enabled = !config.enabled;
    this.saveConfig(interaction.guildId, config);

    await this.showMainSetup(interaction, true);
  },

  async deployApplicationPanel(interaction) {
    const config = this.loadConfig(interaction.guildId);

    if (!config.applicationChannelId) {
      await interaction.reply({
        content: '❌ Please set an application channel first.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    try {
      const channel = await interaction.guild.channels.fetch(config.applicationChannelId);

      const embed = new EmbedBuilder()
        .setTitle('⭐ Application Portal ⭐')
        .setDescription('✨ Welcome to our official application system. Select an application type below to begin the process. ✨')
        .setColor(0x2F3136) // Dark theme matching reference
        .addFields([
          {
            name: '🌟 Application Process',
            value: '1. Select an application type below\n2. Complete the application form\n3. Submit your responses\n4. Wait for staff review',
            inline: false
          },
          {
            name: '⏱️ Estimated Time',
            value: '2-5 minutes',
            inline: true
          },
          {
            name: '🔒 Privacy Notice',
            value: 'All information provided will be kept confidential and reviewed only by authorized staff members.',
            inline: false
          }
        ])
        .setFooter({ text: `© 2025 ${interaction.guild.name} • ${new Date().toLocaleString()}` })
        .setTimestamp();

      // Create dropdown menu with all application types
      const types = Object.keys(config.applicationTypes);
      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('app_select_application_type')
        .setPlaceholder('Select an application type')
        .addOptions(
          types.map(typeId => {
            const type = config.applicationTypes[typeId];
            const isDefault = config.defaultApplicationType === typeId;
            return {
              label: type.name,
              value: typeId,
              description: type.description || `${type.questions?.length || 0} questions`,
              emoji: isDefault ? '⭐' : '🌟'
            };
          })
        );

      const selectRow = new ActionRowBuilder().addComponents(selectMenu);

      const message = await channel.send({
        embeds: [embed],
        components: [selectRow]
      });

      // Save the message ID for future reference
      config.applicationPanelMessageId = message.id;
      this.saveConfig(interaction.guildId, config);

      await interaction.reply({
        content: `✅ Application panel deployed to <#${config.applicationChannelId}>!`,
        flags: MessageFlags.Ephemeral
      });
    } catch (error) {
      console.error('Error deploying application panel:', error);
      await interaction.reply({
        content: '❌ Failed to deploy application panel. Please check the channel permissions.',
        flags: MessageFlags.Ephemeral
      });
    }
  },

  async testApplication(interaction) {
    const ApplicationHandler = require('../utils/applicationHandler');
    await ApplicationHandler.handleApplicationStart(interaction);
  },

  async removeApplicationPanel(interaction) {
    const config = this.loadConfig(interaction.guildId);
    
    if (!config.applicationPanelMessageId || !config.applicationChannelId) {
      await interaction.reply({
        content: '❌ No application panel found to remove.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    try {
      const channel = await interaction.guild.channels.fetch(config.applicationChannelId);
      const message = await channel.messages.fetch(config.applicationPanelMessageId);
      
      await message.delete();
      
      // Clear the message ID from config
      config.applicationPanelMessageId = null;
      this.saveConfig(interaction.guildId, config);

      await interaction.reply({
        content: '✅ Application panel removed successfully! Use the setup command again to see updated status.',
        flags: MessageFlags.Ephemeral
      });

    } catch (error) {
      console.error('Error removing application panel:', error);
      
      // Clear the message ID even if deletion failed (message might already be deleted)
      config.applicationPanelMessageId = null;
      this.saveConfig(interaction.guildId, config);
      
      await interaction.reply({
        content: '⚠️ Application panel reference cleared (message may have been already deleted).',
        flags: MessageFlags.Ephemeral
      });
    }
  },

  async recreateApplicationPanel(interaction) {
    const config = this.loadConfig(interaction.guildId);
    
    if (!config.applicationChannelId) {
      await interaction.reply({
        content: '❌ Please set an application channel first.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    try {
      // First, try to remove the existing panel if it exists
      if (config.applicationPanelMessageId) {
        try {
          const channel = await interaction.guild.channels.fetch(config.applicationChannelId);
          const existingMessage = await channel.messages.fetch(config.applicationPanelMessageId);
          await existingMessage.delete();
        } catch (error) {
          console.log('Existing panel message not found or already deleted');
        }
      }

      // Create new application panel
      const channel = await interaction.guild.channels.fetch(config.applicationChannelId);

      const embed = new EmbedBuilder()
        .setTitle('⭐ Application Portal ⭐')
        .setDescription('✨ Welcome to our official application system. Select an application type below to begin the process. ✨')
        .setColor(0x2F3136) // Dark theme matching reference
        .addFields([
          {
            name: '🌟 Application Process',
            value: '1. Select an application type below\n2. Complete the application form\n3. Submit your responses\n4. Wait for staff review',
            inline: false
          },
          {
            name: '⏱️ Estimated Time',
            value: '2-5 minutes',
            inline: true
          },
          {
            name: '🔒 Privacy Notice',
            value: 'All information provided will be kept confidential and reviewed only by authorized staff members.',
            inline: false
          }
        ])
        .setFooter({ text: `© 2025 ${interaction.guild.name} • ${new Date().toLocaleString()}` })
        .setTimestamp();

      // Create dropdown menu with all application types
      const types = Object.keys(config.applicationTypes);
      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('app_select_application_type')
        .setPlaceholder('Select an application type')
        .addOptions(
          types.map(typeId => {
            const type = config.applicationTypes[typeId];
            const isDefault = config.defaultApplicationType === typeId;
            return {
              label: type.name,
              value: typeId,
              description: type.description || `${type.questions?.length || 0} questions`,
              emoji: isDefault ? '⭐' : '🌟'
            };
          })
        );

      const selectRow = new ActionRowBuilder().addComponents(selectMenu);

      const message = await channel.send({
        embeds: [embed],
        components: [selectRow]
      });

      // Save the new message ID
      config.applicationPanelMessageId = message.id;
      this.saveConfig(interaction.guildId, config);

      await interaction.reply({
        content: `✅ Application panel recreated in <#${config.applicationChannelId}>!`,
        flags: MessageFlags.Ephemeral
      });

      // Main setup will be updated when user runs command again

    } catch (error) {
      console.error('Error recreating application panel:', error);
      await interaction.reply({
        content: '❌ Failed to recreate application panel. Please check the channel permissions.',
        flags: MessageFlags.Ephemeral
      });
    }
  },

  async showClearDataConfirmation(interaction) {
    const config = this.loadConfig(interaction.guildId);
    
    const embed = new EmbedBuilder()
      .setTitle('⚠️ Clear All Application Data')
      .setDescription('This will permanently delete ALL application system data for this server.')
      .setColor(0xE74C3C)
      .addFields([
        {
          name: '🗑️ What will be deleted:',
          value: [
            `• ${config.questions.length} configured questions`,
            `• All role assignments`,
            `• Channel configurations`,
            `• Application panel (if deployed)`,
            `• System settings`
          ].join('\n'),
          inline: false
        },
        {
          name: '⚠️ Warning:',
          value: 'This action cannot be undone! You will need to reconfigure everything from scratch.',
          inline: false
        }
      ]);

    const buttons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_confirm_clear_all')
          .setLabel('Yes, Clear Everything')
          .setStyle(ButtonStyle.Danger)
          .setEmoji('💥'),
        new ButtonBuilder()
          .setCustomId('app_cancel_clear')
          .setLabel('Cancel')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('❌')
      );

    await interaction.update({
      embeds: [embed],
      components: [buttons]
    });
  },

  async handleSpecificButton(interaction) {
    const customId = interaction.customId;

    // Handle add question to specific application
    if (customId.startsWith('app_add_question_to_')) {
      const typeId = customId.replace('app_add_question_to_', '');
      await this.showAddQuestionToApplication(interaction, typeId);
      return;
    }

    // Handle edit questions for specific application
    if (customId.startsWith('app_edit_questions_')) {
      const typeId = customId.replace('app_edit_questions_', '');
      await this.showEditQuestionsForApplication(interaction, typeId);
      return;
    }

    // Handle edit application settings
    if (customId.startsWith('app_edit_app_settings_')) {
      const typeId = customId.replace('app_edit_app_settings_', '');
      await this.showEditApplicationSettings(interaction, typeId);
      return;
    }

    // Handle set review channel
    if (customId.startsWith('app_set_review_channel_')) {
      const typeId = customId.replace('app_set_review_channel_', '');
      await this.showSetReviewChannel(interaction, typeId);
      return;
    }

    // Handle delete application
    if (customId.startsWith('app_delete_application_')) {
      const typeId = customId.replace('app_delete_application_', '');
      await this.showDeleteApplicationConfirmation(interaction, typeId);
      return;
    }

    // Handle confirm delete application
    if (customId.startsWith('app_confirm_delete_')) {
      const typeId = customId.replace('app_confirm_delete_', '');
      await this.deleteApplication(interaction, typeId);
      return;
    }

    // Handle cancel delete application
    if (customId.startsWith('app_cancel_delete_')) {
      const typeId = customId.replace('app_cancel_delete_', '');
      await this.showApplicationManagement(interaction, typeId);
      return;
    }

    // Handle back to application
    if (customId.startsWith('app_back_to_app_')) {
      const typeId = customId.replace('app_back_to_app_', '');
      await this.showApplicationManagement(interaction, typeId);
      return;
    }

    // Handle role assignments for application
    if (customId.startsWith('app_role_assignments_')) {
      const typeId = customId.replace('app_role_assignments_', '');
      await this.showRoleAssignmentsForApplication(interaction, typeId);
      return;
    }

    // Handle role assignment for specific question
    if (customId.startsWith('app_role_assignment_')) {
      const parts = customId.replace('app_role_assignment_', '').split('_');
      const questionIndex = parseInt(parts.pop());
      const typeId = parts.join('_');
      await this.showRoleAssignmentForQuestion(interaction, typeId, questionIndex);
      return;
    }



    switch (customId) {
      case 'app_back_main':
        await this.showMainSetup(interaction, true);
        break;
      case 'app_manage_applications':
        await this.showManageApplications(interaction);
        break;
      case 'app_add_question':
        await this.showAddQuestionModal(interaction);
        break;
      case 'app_edit_question':
        await this.showEditQuestionSelect(interaction);
        break;
      case 'app_delete_question':
        await this.showDeleteQuestionSelect(interaction);
        break;
      default:
        console.log(`Unhandled button: ${customId}`);
    }
  },

  // Legacy method for backward compatibility
  async handleLegacyButtons(interaction) {
    const customId = interaction.customId;
    
    switch (customId) {
      case 'app_back_main':
        await this.showMainSetup(interaction, true);
        break;
      case 'app_add_question':
        await this.showAddQuestionModal(interaction);
        break;
      case 'app_edit_question':
        await this.showEditQuestionSelect(interaction);
        break;
      case 'app_delete_question':
        await this.showDeleteQuestionSelect(interaction);
        break;
      case 'app_start_application':
        const ApplicationHandler = require('../utils/applicationHandler');
        await ApplicationHandler.handleApplicationStart(interaction);
        break;
      default:
        if (customId.startsWith('app_edit_q_')) {
          const questionIndex = parseInt(customId.split('_')[3]);
          await this.showEditQuestionModal(interaction, questionIndex);
        } else if (customId.startsWith('app_delete_q_')) {
          const questionIndex = parseInt(customId.split('_')[3]);
          await this.deleteQuestion(interaction, questionIndex);
        } else if (customId.startsWith('app_role_')) {
          await this.handleRoleAssignmentButton(interaction);
        } else if (customId.startsWith('app_confirm_delete_')) {
          const questionIndex = parseInt(customId.split('_')[3]);
          await this.deleteQuestion(interaction, questionIndex);
        } else if (customId === 'app_cancel_delete') {
          await this.showQuestionsManager(interaction);
        } else if (customId === 'app_back_questions') {
          await this.showQuestionsManager(interaction);
        } else if (customId === 'app_back_roles') {
          await this.showRoleAssignmentSetup(interaction);
        } else if (customId.startsWith('app_text_question_')) {
          const questionIndex = parseInt(customId.split('_')[3]);
          await this.showTextQuestionModal(interaction, questionIndex);
        } else if (customId === 'app_confirm_clear_all') {
          await this.clearAllData(interaction);
        } else if (customId === 'app_cancel_clear') {
          await this.showMainSetup(interaction, true);
        } else if (customId === 'app_edit_success_message') {
          await this.showEditMessageModal(interaction, 'success');
        } else if (customId === 'app_edit_denial_message') {
          await this.showEditMessageModal(interaction, 'denial');
        } else if (customId === 'app_edit_submission_message') {
          await this.showEditMessageModal(interaction, 'submission');
        } else if (customId === 'app_preview_success') {
          await this.previewMessage(interaction, 'success');
        } else if (customId === 'app_preview_denial') {
          await this.previewMessage(interaction, 'denial');
        } else if (customId === 'app_reset_messages') {
          await this.resetMessages(interaction);
        } else if (customId === 'app_confirm_reset_messages') {
          await this.confirmResetMessages(interaction);
        } else if (customId === 'app_cancel_reset_messages') {
          await this.showMessageConfiguration(interaction);
        } else if (customId === 'app_edit_revoke_message') {
          await this.showEditMessageModal(interaction, 'revoke');
        } else if (customId === 'app_preview_revoke') {
          await this.previewMessage(interaction, 'revoke');
        }
    }
  },

  async showAddQuestionModal(interaction) {
    const modal = new ModalBuilder()
      .setCustomId('app_modal_add_question')
      .setTitle('Add New Question');

    const questionInput = new TextInputBuilder()
      .setCustomId('question')
      .setLabel('Question Text')
      .setStyle(TextInputStyle.Paragraph)
      .setRequired(true)
      .setMaxLength(500)
      .setPlaceholder('Enter your question here...');

    const typeInput = new TextInputBuilder()
      .setCustomId('type')
      .setLabel('Question Type')
      .setStyle(TextInputStyle.Short)
      .setRequired(true)
      .setPlaceholder('text or choice')
      .setValue('text');

    const requiredInput = new TextInputBuilder()
      .setCustomId('required')
      .setLabel('Required? (yes/no)')
      .setStyle(TextInputStyle.Short)
      .setRequired(true)
      .setPlaceholder('yes or no')
      .setValue('yes');

    const optionsInput = new TextInputBuilder()
      .setCustomId('options')
      .setLabel('Options (for choice questions, one per line)')
      .setStyle(TextInputStyle.Paragraph)
      .setRequired(false)
      .setPlaceholder('Option 1\nOption 2\nOption 3');

    const placeholderInput = new TextInputBuilder()
      .setCustomId('placeholder')
      .setLabel('Placeholder Text (optional)')
      .setStyle(TextInputStyle.Short)
      .setRequired(false)
      .setPlaceholder('Hint text for the user...');

    const verificationInput = new TextInputBuilder()
      .setCustomId('verification')
      .setLabel('Channel Verification (none/warning/required)')
      .setStyle(TextInputStyle.Short)
      .setRequired(false)
      .setPlaceholder('none, warning, or required')
      .setValue('none');

    modal.addComponents(
      new ActionRowBuilder().addComponents(questionInput),
      new ActionRowBuilder().addComponents(typeInput),
      new ActionRowBuilder().addComponents(requiredInput),
      new ActionRowBuilder().addComponents(optionsInput),
      new ActionRowBuilder().addComponents(verificationInput)
    );

    await interaction.showModal(modal);
  },

  async handleModalSubmit(interaction) {
    const customId = interaction.customId;

    if (customId === 'app_modal_add_question') {
      await this.processAddQuestion(interaction);
    } else if (customId.startsWith('app_modal_edit_question_')) {
      const questionIndex = parseInt(customId.split('_')[4]);
      await this.processEditQuestion(interaction, questionIndex);
    } else if (customId.startsWith('app_modal_channel_')) {
      await this.processChannelSelection(interaction);
    } else if (customId.startsWith('app_modal_role_')) {
      await this.processRoleAssignment(interaction);
    } else if (customId.startsWith('app_modal_message_')) {
      const messageType = customId.split('_')[3];
      await this.processMessageEdit(interaction, messageType);
    }
  },

  async processAddQuestion(interaction) {
    const question = interaction.fields.getTextInputValue('question');
    const type = interaction.fields.getTextInputValue('type').toLowerCase();
    const required = interaction.fields.getTextInputValue('required').toLowerCase() === 'yes';
    
    // Get optional fields with proper error handling
    let optionsText = '';
    try {
      optionsText = interaction.fields.getTextInputValue('options');
    } catch (error) {
      // Field might not exist, default to empty string
      optionsText = '';
    }

    let placeholder = '';
    try {
      placeholder = interaction.fields.getTextInputValue('placeholder');
    } catch (error) {
      // Field might not exist, default to empty string
      placeholder = '';
    }

    // Get verification setting (if provided)
    let verification = 'none';
    try {
      verification = interaction.fields.getTextInputValue('verification')?.toLowerCase() || 'none';
    } catch (error) {
      // Field might not exist in older modals, default to 'none'
      verification = 'none';
    }

    if (!['text', 'choice'].includes(type)) {
      await interaction.reply({
        content: '❌ Question type must be either "text" or "choice".',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    if (!['none', 'warning', 'required'].includes(verification)) {
      await interaction.reply({
        content: '❌ Verification mode must be "none", "warning", or "required".',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const questionObj = {
      question,
      type,
      required,
      placeholder: placeholder || null,
      maxLength: 1000,
      multiline: question.length > 100,
      verifyInChannels: verification !== 'none',
      verificationMode: verification,
      logVerification: verification === 'required'
    };

    if (type === 'choice') {
      if (!optionsText.trim()) {
        await interaction.reply({
          content: '❌ Choice questions must have options. Please provide at least 2 options.',
          flags: MessageFlags.Ephemeral
        });
        return;
      }

      const options = optionsText.split('\n').map(opt => opt.trim()).filter(opt => opt.length > 0);
      if (options.length < 2) {
        await interaction.reply({
          content: '❌ Choice questions must have at least 2 options.',
          flags: MessageFlags.Ephemeral
        });
        return;
      }

      questionObj.options = options;
    }

    const config = this.loadConfig(interaction.guildId);
    config.questions.push(questionObj);
    this.saveConfig(interaction.guildId, config);

    await interaction.reply({
      content: '✅ Question added successfully!' + 
        (verification !== 'none' ? `\n🔍 Channel verification: ${verification}` : ''),
      flags: MessageFlags.Ephemeral
    });

    // Show the questions manager again
    setTimeout(async () => {
      await this.showQuestionsManager(interaction);
    }, 1000);
  },

  async handleSelectMenu(interaction) {
    const customId = interaction.customId;
    const value = interaction.values[0];

    if (customId === 'app_select_channel_type') {
      await this.showChannelSelectionModal(interaction, value);
    } else if (customId === 'app_select_question_roles') {
      const questionIndex = parseInt(value);
      await this.showRoleAssignmentForQuestion(interaction, questionIndex);
    } else if (customId.startsWith('app_select_edit_question')) {
      const questionIndex = parseInt(value);
      await this.showEditQuestionModal(interaction, questionIndex);
    } else if (customId.startsWith('app_select_delete_question')) {
      const questionIndex = parseInt(value);
      await this.confirmDeleteQuestion(interaction, questionIndex);
    } else if (customId.startsWith('app_select_role_option_')) {
      const questionIndex = parseInt(customId.split('_')[4]);
      const option = value;
      await this.showRoleSelectionModal(interaction, questionIndex, option);
    }
  },

  async showChannelSelectionModal(interaction, channelType) {
    const modal = new ModalBuilder()
      .setCustomId(`app_modal_channel_${channelType}`)
      .setTitle(`Set ${channelType.charAt(0).toUpperCase() + channelType.slice(1)} Channel${channelType === 'verification' ? 's' : ''}`);

    let label, placeholder;
    if (channelType === 'verification') {
      label = 'Verification Channels (comma separated)';
      placeholder = '#general, #chat, 123456789012345678';
    } else {
      label = 'Channel ID or #channel-name';
      placeholder = '123456789012345678 or #general';
    }

    const channelInput = new TextInputBuilder()
      .setCustomId('channel_id')
      .setLabel(label)
      .setStyle(channelType === 'verification' ? TextInputStyle.Paragraph : TextInputStyle.Short)
      .setRequired(true)
      .setPlaceholder(placeholder);

    modal.addComponents(new ActionRowBuilder().addComponents(channelInput));
    await interaction.showModal(modal);
  },

  async processChannelSelection(interaction) {
    const channelType = interaction.customId.split('_')[3];
    const channelInput = interaction.fields.getTextInputValue('channel_id');

    if (channelType === 'verification') {
      // Handle verification channels (multiple channels separated by commas or spaces)
      const channelInputs = channelInput.split(/[,\s]+/).filter(input => input.trim().length > 0);
      const channelIds = [];
      const invalidChannels = [];

      for (const input of channelInputs) {
        let channelId;
        if (input.startsWith('#')) {
          const channelName = input.slice(1);
          const channel = interaction.guild.channels.cache.find(ch => ch.name === channelName);
          if (channel) {
            channelId = channel.id;
          } else {
            invalidChannels.push(input);
            continue;
          }
        } else {
          channelId = input.trim();
        }

        try {
          const channel = await interaction.guild.channels.fetch(channelId);
          if (channel && channel.isTextBased()) {
            channelIds.push(channelId);
          } else {
            invalidChannels.push(input);
          }
        } catch (error) {
          invalidChannels.push(input);
        }
      }

      if (invalidChannels.length > 0) {
        await interaction.reply({
          content: `❌ Invalid channels: ${invalidChannels.join(', ')}\n${channelIds.length > 0 ? `✅ Valid channels found: ${channelIds.length}` : ''}`,
          flags: MessageFlags.Ephemeral
        });
        return;
      }

      const config = this.loadConfig(interaction.guildId);
      config.verificationChannelIds = channelIds;
      this.saveConfig(interaction.guildId, config);

      await interaction.reply({
        content: `✅ Verification channels set! Bot will search in ${channelIds.length} channel${channelIds.length !== 1 ? 's' : ''} for user data.`,
        flags: MessageFlags.Ephemeral,
        components: [
          new ActionRowBuilder()
            .addComponents(
              new ButtonBuilder()
                .setCustomId('app_setup_channels')
                .setLabel('Back to Channel Setup')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('📝'),
              new ButtonBuilder()
                .setCustomId('app_back_main')
                .setLabel('Back to Main')
                .setStyle(ButtonStyle.Primary)
                .setEmoji('⬅️')
            )
        ]
      });
      return;
    }

    // Handle single channel types (application, log)
    let channelId;
    if (channelInput.startsWith('#')) {
      const channelName = channelInput.slice(1);
      const channel = interaction.guild.channels.cache.find(ch => ch.name === channelName);
      if (!channel) {
        await interaction.reply({
          content: `❌ Channel "${channelName}" not found.`,
          flags: MessageFlags.Ephemeral
        });
        return;
      }
      channelId = channel.id;
    } else {
      channelId = channelInput;
    }

    try {
      const channel = await interaction.guild.channels.fetch(channelId);
      if (!channel) {
        await interaction.reply({
          content: '❌ Invalid channel ID.',
          flags: MessageFlags.Ephemeral
        });
        return;
      }

      const config = this.loadConfig(interaction.guildId);
      
      // For log channel, also set it as admin channel (combined functionality)
      if (channelType === 'log') {
        config.logChannelId = channelId;
        config.adminChannelId = channelId; // Combined admin and log channel
      } else {
        config[`${channelType}ChannelId`] = channelId;
      }
      
      this.saveConfig(interaction.guildId, config);

      await interaction.reply({
        content: `✅ ${channelType.charAt(0).toUpperCase() + channelType.slice(1)} channel set to <#${channelId}>!` +
          (channelType === 'log' ? '\n📊 This channel will be used for both logs and admin notifications.' : ''),
        flags: MessageFlags.Ephemeral,
        components: [
          new ActionRowBuilder()
            .addComponents(
              new ButtonBuilder()
                .setCustomId('app_setup_channels')
                .setLabel('Back to Channel Setup')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('📝'),
              new ButtonBuilder()
                .setCustomId('app_back_main')
                .setLabel('Back to Main')
                .setStyle(ButtonStyle.Primary)
                .setEmoji('⬅️')
            )
        ]
      });
    } catch (error) {
      await interaction.reply({
        content: '❌ Failed to set channel. Please check the channel ID and permissions.',
        flags: MessageFlags.Ephemeral
      });
    }
  }, 

  async showRoleAssignmentForQuestion(interaction, questionIndex) {
    const config = this.loadConfig(interaction.guildId);
    const question = config.questions[questionIndex];

    if (!question) {
      await interaction.reply({
        content: '❌ Question not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const embed = new EmbedBuilder()
      .setTitle(`🎭 Role Assignment - Question ${questionIndex + 1}`)
      .setDescription(`**Question:** ${question.question}`)
      .setColor(0xE67E22);

    const currentAssignments = config.roleAssignments[questionIndex] || {};

    if (question.type === 'choice') {
      const assignmentInfo = question.options.map(option => {
        const roleId = currentAssignments[option];
        return `**${option}** → ${roleId ? `<@&${roleId}>` : 'No role assigned'}`;
      }).join('\n');

      embed.addFields([
        {
          name: '📋 Current Role Assignments',
          value: assignmentInfo || 'No assignments configured',
          inline: false
        }
      ]);

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId(`app_select_role_option_${questionIndex}`)
        .setPlaceholder('Select option to assign role...')
        .addOptions(
          question.options.map(option => ({
            label: option,
            value: option,
            description: currentAssignments[option] ? 'Has role assigned' : 'No role assigned',
            emoji: currentAssignments[option] ? '✅' : '❌'
          }))
        );

      const selectRow = new ActionRowBuilder().addComponents(selectMenu);

      await interaction.update({
        embeds: [embed],
        components: [selectRow, this.getBackToRolesButton()]
      });
    } else {
      embed.addFields([
        {
          name: '❌ Text Questions',
          value: 'Role assignment is only available for choice questions. Text questions cannot have automatic role assignments.',
          inline: false
        }
      ]);

      await interaction.update({
        embeds: [embed],
        components: [this.getBackToRolesButton()]
      });
    }
  },

  async showEditQuestionSelect(interaction) {
    const config = this.loadConfig(interaction.guildId);

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId('app_select_edit_question')
      .setPlaceholder('Select question to edit...')
      .addOptions(
        config.questions.map((q, i) => ({
          label: `Question ${i + 1}: ${q.question.substring(0, 50)}${q.question.length > 50 ? '...' : ''}`,
          value: i.toString(),
          description: `${q.type} question, ${q.required ? 'required' : 'optional'}`,
          emoji: '✏️'
        }))
      );

    const selectRow = new ActionRowBuilder().addComponents(selectMenu);
    const backButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_back_questions')
          .setLabel('Back to Questions')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️')
      );

    await interaction.update({
      content: 'Select a question to edit:',
      embeds: [],
      components: [selectRow, backButton]
    });
  },

  async showDeleteQuestionSelect(interaction) {
    const config = this.loadConfig(interaction.guildId);

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId('app_select_delete_question')
      .setPlaceholder('Select question to delete...')
      .addOptions(
        config.questions.map((q, i) => ({
          label: `Question ${i + 1}: ${q.question.substring(0, 50)}${q.question.length > 50 ? '...' : ''}`,
          value: i.toString(),
          description: `${q.type} question, ${q.required ? 'required' : 'optional'}`,
          emoji: '🗑️'
        }))
      );

    const selectRow = new ActionRowBuilder().addComponents(selectMenu);
    const backButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_back_questions')
          .setLabel('Back to Questions')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️')
      );

    await interaction.update({
      content: '⚠️ Select a question to delete (this cannot be undone):',
      embeds: [],
      components: [selectRow, backButton]
    });
  },

  async showEditQuestionModal(interaction, questionIndex) {
    const config = this.loadConfig(interaction.guildId);
    const question = config.questions[questionIndex];

    if (!question) {
      await interaction.reply({
        content: '❌ Question not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const modal = new ModalBuilder()
      .setCustomId(`app_modal_edit_question_${questionIndex}`)
      .setTitle(`Edit Question ${questionIndex + 1}`);

    const questionInput = new TextInputBuilder()
      .setCustomId('question')
      .setLabel('Question Text')
      .setStyle(TextInputStyle.Paragraph)
      .setRequired(true)
      .setMaxLength(500)
      .setValue(question.question);

    const typeInput = new TextInputBuilder()
      .setCustomId('type')
      .setLabel('Question Type')
      .setStyle(TextInputStyle.Short)
      .setRequired(true)
      .setValue(question.type);

    const requiredInput = new TextInputBuilder()
      .setCustomId('required')
      .setLabel('Required? (yes/no)')
      .setStyle(TextInputStyle.Short)
      .setRequired(true)
      .setValue(question.required ? 'yes' : 'no');

    const optionsInput = new TextInputBuilder()
      .setCustomId('options')
      .setLabel('Options (for choice questions, one per line)')
      .setStyle(TextInputStyle.Paragraph)
      .setRequired(false)
      .setValue(question.options ? question.options.join('\n') : '');

    const placeholderInput = new TextInputBuilder()
      .setCustomId('placeholder')
      .setLabel('Placeholder Text (optional)')
      .setStyle(TextInputStyle.Short)
      .setRequired(false)
      .setValue(question.placeholder || '');

    modal.addComponents(
      new ActionRowBuilder().addComponents(questionInput),
      new ActionRowBuilder().addComponents(typeInput),
      new ActionRowBuilder().addComponents(requiredInput),
      new ActionRowBuilder().addComponents(optionsInput),
      new ActionRowBuilder().addComponents(placeholderInput)
    );

    await interaction.showModal(modal);
  },

  async processEditQuestion(interaction, questionIndex) {
    const question = interaction.fields.getTextInputValue('question');
    const type = interaction.fields.getTextInputValue('type').toLowerCase();
    const required = interaction.fields.getTextInputValue('required').toLowerCase() === 'yes';
    const optionsText = interaction.fields.getTextInputValue('options');
    const placeholder = interaction.fields.getTextInputValue('placeholder');

    if (!['text', 'choice'].includes(type)) {
      await interaction.reply({
        content: '❌ Question type must be either "text" or "choice".',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const questionObj = {
      question,
      type,
      required,
      placeholder: placeholder || null,
      maxLength: 1000,
      multiline: question.length > 100
    };

    if (type === 'choice') {
      if (!optionsText.trim()) {
        await interaction.reply({
          content: '❌ Choice questions must have options. Please provide at least 2 options.',
          flags: MessageFlags.Ephemeral
        });
        return;
      }

      const options = optionsText.split('\n').map(opt => opt.trim()).filter(opt => opt.length > 0);
      if (options.length < 2) {
        await interaction.reply({
          content: '❌ Choice questions must have at least 2 options.',
          flags: MessageFlags.Ephemeral
        });
        return;
      }

      questionObj.options = options;
    }

    const config = this.loadConfig(interaction.guildId);
    config.questions[questionIndex] = questionObj;
    this.saveConfig(interaction.guildId, config);

    await interaction.reply({
      content: '✅ Question updated successfully!',
      flags: MessageFlags.Ephemeral
    });

    setTimeout(async () => {
      await this.showQuestionsManager(interaction);
    }, 1000);
  },

  async confirmDeleteQuestion(interaction, questionIndex) {
    const config = this.loadConfig(interaction.guildId);
    const question = config.questions[questionIndex];

    if (!question) {
      await interaction.reply({
        content: '❌ Question not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const embed = new EmbedBuilder()
      .setTitle('⚠️ Confirm Question Deletion')
      .setDescription(`Are you sure you want to delete this question? This action cannot be undone.`)
      .setColor(0xE74C3C)
      .addFields([
        {
          name: 'Question to Delete',
          value: `**${question.question}**\nType: ${question.type}\nRequired: ${question.required ? 'Yes' : 'No'}`,
          inline: false
        }
      ]);

    const buttons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`app_confirm_delete_${questionIndex}`)
          .setLabel('Yes, Delete')
          .setStyle(ButtonStyle.Danger)
          .setEmoji('🗑️'),
        new ButtonBuilder()
          .setCustomId('app_cancel_delete')
          .setLabel('Cancel')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('❌')
      );

    await interaction.update({
      embeds: [embed],
      components: [buttons]
    });
  },

  async deleteQuestion(interaction, questionIndex) {
    const config = this.loadConfig(interaction.guildId);

    if (questionIndex < 0 || questionIndex >= config.questions.length) {
      await interaction.reply({
        content: '❌ Invalid question index.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Remove the question
    config.questions.splice(questionIndex, 1);

    // Reindex role assignments
    const newRoleAssignments = {};
    Object.keys(config.roleAssignments).forEach(key => {
      const index = parseInt(key);
      if (index < questionIndex) {
        newRoleAssignments[index] = config.roleAssignments[key];
      } else if (index > questionIndex) {
        newRoleAssignments[index - 1] = config.roleAssignments[key];
      }
      // Skip the deleted question's assignments
    });
    config.roleAssignments = newRoleAssignments;

    this.saveConfig(interaction.guildId, config);

    await interaction.update({
      content: '✅ Question deleted successfully!',
      embeds: [],
      components: []
    });

    setTimeout(async () => {
      await this.showQuestionsManager(interaction);
    }, 1000);
  },

  getBackToRolesButton() {
    return new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_back_roles')
          .setLabel('Back to Role Setup')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️')
      );
  },

  async showRoleSelectionModal(interaction, questionIndex, option) {
    const modal = new ModalBuilder()
      .setCustomId(`app_modal_role_${questionIndex}_${option}`)
      .setTitle(`Assign Role for "${option}"`);

    const roleInput = new TextInputBuilder()
      .setCustomId('role_id')
      .setLabel('Role ID or @role-name')
      .setStyle(TextInputStyle.Short)
      .setRequired(true)
      .setPlaceholder('123456789012345678 or @Member');

    modal.addComponents(new ActionRowBuilder().addComponents(roleInput));
    await interaction.showModal(modal);
  },

  async processRoleAssignment(interaction) {
    const customIdParts = interaction.customId.split('_');
    const questionIndex = parseInt(customIdParts[3]);
    const option = customIdParts.slice(4).join('_');
    const roleInput = interaction.fields.getTextInputValue('role_id');

    let roleId;
    if (roleInput.startsWith('@')) {
      const roleName = roleInput.slice(1);
      const role = interaction.guild.roles.cache.find(r => r.name === roleName);
      if (!role) {
        await interaction.reply({
          content: `❌ Role "${roleName}" not found.`,
          flags: MessageFlags.Ephemeral
        });
        return;
      }
      roleId = role.id;
    } else {
      roleId = roleInput;
    }

    try {
      const role = await interaction.guild.roles.fetch(roleId);
      if (!role) {
        await interaction.reply({
          content: '❌ Invalid role ID.',
          flags: MessageFlags.Ephemeral
        });
        return;
      }

      const config = this.loadConfig(interaction.guildId);
      if (!config.roleAssignments[questionIndex]) {
        config.roleAssignments[questionIndex] = {};
      }
      config.roleAssignments[questionIndex][option] = roleId;
      this.saveConfig(interaction.guildId, config);

      await interaction.reply({
        content: `✅ Role <@&${roleId}> assigned to option "${option}"!`,
        flags: MessageFlags.Ephemeral
      });

      setTimeout(async () => {
        await this.showRoleAssignmentForQuestion(interaction, questionIndex);
      }, 1000);
    } catch (error) {
      await interaction.reply({
        content: '❌ Failed to assign role. Please check the role ID and permissions.',
        flags: MessageFlags.Ephemeral
      });
    }
  },

  async clearAllData(interaction) {
    const config = this.loadConfig(interaction.guildId);
    
    try {
      // First, try to remove the application panel if it exists
      if (config.applicationPanelMessageId && config.applicationChannelId) {
        try {
          const channel = await interaction.guild.channels.fetch(config.applicationChannelId);
          const message = await channel.messages.fetch(config.applicationPanelMessageId);
          await message.delete();
        } catch (error) {
          console.log('Application panel message not found or already deleted');
        }
      }

      // Reset config to default
      const defaultConfig = this.getDefaultConfig();
      this.saveConfig(interaction.guildId, defaultConfig);

      // Also clear application submissions for this guild
      try {
        const submissionsFile = path.join(__dirname, '..', 'application_submissions.json');
        if (fs.existsSync(submissionsFile)) {
          const submissionsData = JSON.parse(fs.readFileSync(submissionsFile, 'utf8'));
          if (submissionsData[interaction.guildId]) {
            delete submissionsData[interaction.guildId];
            fs.writeFileSync(submissionsFile, JSON.stringify(submissionsData, null, 2));
          }
        }
      } catch (error) {
        console.error('Error clearing submissions data:', error);
      }

      const embed = new EmbedBuilder()
        .setTitle('✅ All Data Cleared Successfully')
        .setDescription('All application system data has been permanently deleted.')
        .setColor(0x00FF00)
        .addFields([
          {
            name: '🗑️ Cleared Data:',
            value: [
              '• All questions and configurations',
              '• Role assignments',
              '• Channel settings',
              '• Application panel removed',
              '• All user submissions',
              '• System settings reset'
            ].join('\n'),
            inline: false
          },
          {
            name: '🔄 Next Steps:',
            value: 'You can now reconfigure the application system from scratch using the buttons below.',
            inline: false
          }
        ]);

      const buttons = new ActionRowBuilder()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('app_back_main')
            .setLabel('Back to Setup')
            .setStyle(ButtonStyle.Primary)
            .setEmoji('🛠️'),
          new ButtonBuilder()
            .setCustomId('app_setup_questions')
            .setLabel('Add Questions')
            .setStyle(ButtonStyle.Success)
            .setEmoji('❓')
        );

      await interaction.update({
        embeds: [embed],
        components: [buttons]
      });

    } catch (error) {
      console.error('Error clearing all data:', error);
      await interaction.reply({
        content: '❌ An error occurred while clearing data. Some data may not have been cleared completely.',
        flags: MessageFlags.Ephemeral
      });
    }
  },



  async showEditMessageModal(interaction, messageType) {
    const config = this.loadConfig(interaction.guildId);
    
    // Ensure messages object exists
    if (!config.messages) {
      const defaultConfig = this.getDefaultConfig();
      config.messages = defaultConfig.messages;
      this.saveConfig(interaction.guildId, config);
    }

    // Ensure all message types exist
    const defaultMessages = this.getDefaultConfig().messages;
    if (!config.messages.success) config.messages.success = defaultMessages.success;
    if (!config.messages.denial) config.messages.denial = defaultMessages.denial;
    if (!config.messages.submission) config.messages.submission = defaultMessages.submission;
    if (!config.messages.revoke) config.messages.revoke = defaultMessages.revoke;

    const message = config.messages[messageType];
    const messageTypeNames = {
      success: 'Approval Message',
      denial: 'Denial Message',
      submission: 'Form Submission Message',
      revoke: 'Revoke Message'
    };

    // Fallback if message is still undefined
    if (!message) {
      console.error(`Message type '${messageType}' not found in config`);
      await interaction.reply({
        content: `❌ Error: Message type '${messageType}' not found. Please try again.`,
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const modal = new ModalBuilder()
      .setCustomId(`app_modal_message_${messageType}`)
      .setTitle(`Edit ${messageTypeNames[messageType]}`);

    const titleInput = new TextInputBuilder()
      .setCustomId('title')
      .setLabel('Message Title')
      .setStyle(TextInputStyle.Short)
      .setRequired(true)
      .setMaxLength(256)
      .setValue(message.title)
      .setPlaceholder('Enter the embed title...');

    const descriptionInput = new TextInputBuilder()
      .setCustomId('description')
      .setLabel('Message Description')
      .setStyle(TextInputStyle.Paragraph)
      .setRequired(true)
      .setMaxLength(4000)
      .setValue(message.description)
      .setPlaceholder('Enter the embed description...');

    const footerInput = new TextInputBuilder()
      .setCustomId('footer')
      .setLabel('Footer Text (optional)')
      .setStyle(TextInputStyle.Short)
      .setRequired(false)
      .setMaxLength(2048)
      .setValue(message.footer || '')
      .setPlaceholder('Enter footer text (leave empty for no footer)...');

    // Set placeholders based on message type
    let placeholderText = '';
    if (messageType === 'success') {
      placeholderText = '{username} - User\'s display name\n{form_data} - User\'s form responses';
    } else if (messageType === 'denial') {
      placeholderText = '{username} - User\'s display name\n{reason} - Reason for denial\n{form_data} - User\'s form responses';
    } else if (messageType === 'submission') {
      placeholderText = '{username} - User\'s display name\n{form_data} - User\'s form responses';
    } else if (messageType === 'revoke') {
      placeholderText = '{username} - User\'s display name\n{server_name} - Server name\n{reason} - Reason for revocation';
    }

    const placeholdersInput = new TextInputBuilder()
      .setCustomId('placeholders')
      .setLabel('Available Placeholders (info only)')
      .setStyle(TextInputStyle.Paragraph)
      .setRequired(false)
      .setValue(placeholderText)
      .setPlaceholder('These placeholders can be used in title/description');

    modal.addComponents(
      new ActionRowBuilder().addComponents(titleInput),
      new ActionRowBuilder().addComponents(descriptionInput),
      new ActionRowBuilder().addComponents(footerInput),
      new ActionRowBuilder().addComponents(placeholdersInput)
    );

    await interaction.showModal(modal);
  },

  async processMessageEdit(interaction, messageType) {
    const config = this.loadConfig(interaction.guildId);
    
    // Ensure messages object exists
    if (!config.messages) {
      config.messages = this.getDefaultConfig().messages;
    }

    const title = interaction.fields.getTextInputValue('title');
    const description = interaction.fields.getTextInputValue('description');
    let footer = '';
    
    try {
      footer = interaction.fields.getTextInputValue('footer');
    } catch (error) {
      // Footer field might be empty
      footer = '';
    }

    // Update the message configuration
    config.messages[messageType] = {
      title: title,
      description: description,
      footer: footer || null
    };

    this.saveConfig(interaction.guildId, config);

    const messageTypeNames = {
      success: 'Approval Message',
      denial: 'Denial Message',
      submission: 'Form Submission Message',
      revoke: 'Revoke Message'
    };

    await interaction.reply({
      content: `✅ ${messageTypeNames[messageType]} updated successfully!`,
      flags: MessageFlags.Ephemeral
    });

    // Return to message configuration after a brief delay
    setTimeout(async () => {
      try {
        await this.showMessageConfiguration(interaction);
      } catch (error) {
        console.error('Error returning to message configuration:', error);
      }
    }, 1500);
  },

  async previewMessage(interaction, messageType) {
    const config = this.loadConfig(interaction.guildId);
    
    // Ensure messages object exists
    if (!config.messages) {
      config.messages = this.getDefaultConfig().messages;
    }

    const message = config.messages[messageType];
    const messageTypeNames = {
      success: 'Approval Message Preview',
      denial: 'Denial Message Preview',
      submission: 'Form Submission Message Preview',
      revoke: 'Revoke Message Preview'
    };

    // Replace placeholders with example data
    let title = message.title;
    let description = message.description;
    let footer = message.footer;

    // Set placeholders based on message type
    let placeholders = {};
    if (messageType === 'revoke') {
      placeholders = {
        '{username}': interaction.user.displayName || interaction.user.username,
        '{server_name}': interaction.guild.name,
        '{reason}': 'Example: Violation of server rules'
      };
    } else {
      placeholders = {
        '{username}': interaction.user.displayName || interaction.user.username,
        '{reason}': 'Example: Background verification could not be completed',
        '{form_data}': 'Example form responses would appear here'
      };
    }

    // Replace placeholders in title, description, and footer
    Object.entries(placeholders).forEach(([placeholder, value]) => {
      title = title.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), value);
      description = description.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), value);
      if (footer) {
        footer = footer.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), value);
      }
    });

    const colors = {
      success: 0x00AE86,
      denial: 0xE74C3C,
      submission: 0x3498DB,
      revoke: 0xE74C3C,
      pending: 0xF39C12
    };

    const previewEmbed = new EmbedBuilder()
      .setTitle(title)
      .setDescription(description)
      .setColor(colors[messageType]);

    if (footer) {
      previewEmbed.setFooter({ text: footer });
    }

    const embed = new EmbedBuilder()
      .setTitle(`👁️ ${messageTypeNames[messageType]}`)
      .setDescription('This is how your message will appear to users:')
      .setColor(0x9B59B6);

    await interaction.reply({
      content: '**Preview:**',
      embeds: [embed, previewEmbed],
      flags: MessageFlags.Ephemeral
    });
  },

  async resetMessages(interaction) {
    const embed = new EmbedBuilder()
      .setTitle('🔄 Reset Messages to Defaults')
      .setDescription('This will reset all custom messages back to the default templates.')
      .setColor(0xE74C3C)
      .addFields([
        {
          name: '⚠️ Warning',
          value: 'This will overwrite all your custom message configurations. This action cannot be undone.',
          inline: false
        }
      ]);

    const buttons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_confirm_reset_messages')
          .setLabel('Yes, Reset Messages')
          .setStyle(ButtonStyle.Danger)
          .setEmoji('🔄'),
        new ButtonBuilder()
          .setCustomId('app_cancel_reset_messages')
          .setLabel('Cancel')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('❌')
      );

    await interaction.update({
      embeds: [embed],
      components: [buttons]
    });
  },

  async confirmResetMessages(interaction) {
    const config = this.loadConfig(interaction.guildId);
    
    // Reset messages to defaults
    config.messages = this.getDefaultConfig().messages;
    this.saveConfig(interaction.guildId, config);

    await interaction.reply({
      content: '✅ Messages have been reset to defaults!',
      flags: MessageFlags.Ephemeral
    });

    // Return to message configuration
    setTimeout(async () => {
      try {
        await this.showMessageConfiguration(interaction);
      } catch (error) {
        console.error('Error returning to message configuration:', error);
      }
    }, 1500);
  },

  // Application Types Management Methods
  async showApplicationTypesManager(interaction) {
    const config = this.loadConfig(interaction.guildId);
    
    // Initialize applicationTypes if it doesn't exist
    if (!config.applicationTypes) {
      config.applicationTypes = {};
      this.saveConfig(interaction.guildId, config);
    }

    const types = Object.keys(config.applicationTypes);
    
    const embed = new EmbedBuilder()
      .setTitle('📋 Application Types Manager')
      .setDescription('Manage different types of applications for your server.')
      .setColor(0x3498DB);

    if (types.length === 0) {
      embed.addFields([
        {
          name: '📝 No Application Types',
          value: 'No application types have been created yet. Click "Create New Type" to get started.',
          inline: false
        }
      ]);
    } else {
      const typesList = types.map(typeId => {
        const type = config.applicationTypes[typeId];
        const questionCount = type.questions ? type.questions.length : 0;
        const isDefault = config.defaultApplicationType === typeId;
        return `**${type.name}** ${isDefault ? '(Default)' : ''}\n└ ${questionCount} questions • ID: ${typeId}`;
      }).join('\n\n');

      embed.addFields([
        {
          name: '📋 Current Application Types',
          value: typesList,
          inline: false
        }
      ]);
    }

    const buttons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_create_type')
          .setLabel('Create New Type')
          .setStyle(ButtonStyle.Success)
          .setEmoji('➕'),
        new ButtonBuilder()
          .setCustomId('app_edit_type')
          .setLabel('Edit Type')
          .setStyle(ButtonStyle.Primary)
          .setEmoji('✏️')
          .setDisabled(types.length === 0),
        new ButtonBuilder()
          .setCustomId('app_delete_type')
          .setLabel('Delete Type')
          .setStyle(ButtonStyle.Danger)
          .setEmoji('🗑️')
          .setDisabled(types.length === 0)
      );

    const controlButtons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_set_default_type')
          .setLabel('Set Default Type')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⭐')
          .setDisabled(types.length === 0),
        new ButtonBuilder()
          .setCustomId('app_migrate_legacy')
          .setLabel('Migrate Legacy Questions')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('🔄')
          .setDisabled(!config.questions || config.questions.length === 0)
      );

    const backButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_back_main')
          .setLabel('Back to Main')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️')
      );

    await interaction.update({
      embeds: [embed],
      components: [buttons, controlButtons, backButton]
    });
  },

  async showCreateApplicationType(interaction) {
    const modal = new ModalBuilder()
      .setCustomId('app_create_type_modal')
      .setTitle('Create New Application Type');

    const nameInput = new TextInputBuilder()
      .setCustomId('type_name')
      .setLabel('Application Type Name')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('e.g., Staff Application, Whitelist Application')
      .setRequired(true)
      .setMaxLength(50);

    const descriptionInput = new TextInputBuilder()
      .setCustomId('type_description')
      .setLabel('Description (Optional)')
      .setStyle(TextInputStyle.Paragraph)
      .setPlaceholder('Brief description of this application type...')
      .setRequired(false)
      .setMaxLength(500);

    const successTitleInput = new TextInputBuilder()
      .setCustomId('success_title')
      .setLabel('Success Message Title')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('e.g., ✅ Application Approved!')
      .setRequired(true)
      .setMaxLength(100);

    const successDescInput = new TextInputBuilder()
      .setCustomId('success_description')
      .setLabel('Success Message Description')
      .setStyle(TextInputStyle.Paragraph)
      .setPlaceholder('Message shown when application is approved...')
      .setRequired(true)
      .setMaxLength(500);

    const denialTitleInput = new TextInputBuilder()
      .setCustomId('denial_title')
      .setLabel('Denial Message Title')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('e.g., ❌ Application Denied')
      .setRequired(true)
      .setMaxLength(100);

    modal.addComponents(
      new ActionRowBuilder().addComponents(nameInput),
      new ActionRowBuilder().addComponents(descriptionInput),
      new ActionRowBuilder().addComponents(successTitleInput),
      new ActionRowBuilder().addComponents(successDescInput),
      new ActionRowBuilder().addComponents(denialTitleInput)
    );

    await interaction.showModal(modal);
  },

  async handleCreateTypeModal(interaction) {
    const config = this.loadConfig(interaction.guildId);
    
    if (!config.applicationTypes) {
      config.applicationTypes = {};
    }

    const typeName = interaction.fields.getTextInputValue('type_name');
    const typeDescription = interaction.fields.getTextInputValue('type_description') || '';
    const successTitle = interaction.fields.getTextInputValue('success_title');
    const successDescription = interaction.fields.getTextInputValue('success_description');
    const denialTitle = interaction.fields.getTextInputValue('denial_title');

    // Generate unique ID for the application type
    const typeId = `type_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create new application type
    config.applicationTypes[typeId] = {
      name: typeName,
      description: typeDescription,
      questions: [],
      messages: {
        success: {
          title: successTitle,
          description: successDescription,
          footer: null
        },
        denial: {
          title: denialTitle,
          description: 'Please try again or contact staff for help.',
          footer: null
        },
        submission: {
          title: '📄 Application Summary',
          description: 'Here is a summary of your submitted application.',
          footer: 'Thank you for your submission!'
        }
      },
      roleAssignments: {},
      reviewChannelId: null,
      createdAt: new Date().toISOString()
    };

    // Set as default if it's the first type
    if (Object.keys(config.applicationTypes).length === 1) {
      config.defaultApplicationType = typeId;
    }

    this.saveConfig(interaction.guildId, config);

    await interaction.reply({
      content: `✅ Application type "${typeName}" created successfully! Use the setup command again to manage your application types.`,
      flags: MessageFlags.Ephemeral
    });
  },

  async showEditApplicationType(interaction) {
    const config = this.loadConfig(interaction.guildId);
    const types = Object.keys(config.applicationTypes || {});

    if (types.length === 0) {
      await interaction.reply({
        content: '❌ No application types found to edit.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId('app_select_edit_type')
      .setPlaceholder('Select application type to edit...')
      .addOptions(
        types.map(typeId => {
          const type = config.applicationTypes[typeId];
          return {
            label: type.name,
            value: typeId,
            description: `${type.questions?.length || 0} questions`,
            emoji: '✏️'
          };
        })
      );

    const selectRow = new ActionRowBuilder().addComponents(selectMenu);

    const backButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_back_types')
          .setLabel('Back to Types')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️')
      );

    await interaction.update({
      content: 'Select an application type to edit:',
      embeds: [],
      components: [selectRow, backButton]
    });
  },

  async showTypeEditor(interaction, typeId) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type) {
      await interaction.reply({
        content: '❌ Application type not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const embed = new EmbedBuilder()
      .setTitle(`✏️ Editing: ${type.name}`)
      .setDescription(type.description || 'No description')
      .setColor(0xF39C12)
      .addFields([
        {
          name: '📝 Questions',
          value: `${type.questions?.length || 0} configured`,
          inline: true
        },
        {
          name: '🎭 Role Assignments',
          value: `${Object.keys(type.roleAssignments || {}).length} configured`,
          inline: true
        },
        {
          name: '📊 Review Channel',
          value: type.reviewChannelId ? `<#${type.reviewChannelId}>` : 'Not set',
          inline: true
        }
      ]);

    const buttons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`app_edit_type_questions_${typeId}`)
          .setLabel('Manage Questions')
          .setStyle(ButtonStyle.Primary)
          .setEmoji('❓'),
        new ButtonBuilder()
          .setCustomId(`app_edit_type_messages_${typeId}`)
          .setLabel('Edit Messages')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('💬'),
        new ButtonBuilder()
          .setCustomId(`app_edit_type_roles_${typeId}`)
          .setLabel('Role Assignment')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('🎭')
      );

    const settingsButtons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`app_edit_type_settings_${typeId}`)
          .setLabel('Type Settings')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⚙️'),
        new ButtonBuilder()
          .setCustomId(`app_set_review_channel_${typeId}`)
          .setLabel('Set Review Channel')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('📊')
      );

    const backButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_back_types')
          .setLabel('Back to Types')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️')
      );

    await interaction.update({
      embeds: [embed],
      components: [buttons, settingsButtons, backButton]
    });
  },

  // Update the default config to support application types
  getDefaultConfig() {
    return {
      enabled: false,
      questions: [], // Legacy questions for backward compatibility
      applicationTypes: {}, // New multi-type system
      defaultApplicationType: null,
      roleAssignments: {},
      applicationChannelId: null,
      logChannelId: null,
      adminChannelId: null,
      applicationPanelMessageId: null,
      verificationChannelIds: [],
      logApplicationVerification: false,
      messages: {
        success: {
          title: '✅ Your application has been accepted.',
          description: 'Thank you for completing the application process.',
          footer: null
        },
        denial: {
          title: '❌ Verification failed. Please try again or contact staff for help.',
          description: 'Your application could not be processed at this time.',
          footer: null
        },
        submission: {
          title: '📄 Application Summary',
          description: 'Here is a summary of your submitted application.',
          footer: 'Thank you for your submission!'
        },
        revoke: {
          title: '🚫 Access Revoked',
          description: 'Your access has been revoked.',
          footer: 'If you believe this was done in error, please contact the server staff.'
        },
        pending: {
          title: '⏳ Application Under Review',
          description: 'Your application is currently being reviewed by our staff.',
          footer: 'You will be contacted if additional information is needed.'
        }
      }
    };
  },

  // Additional methods for multi-application type system
  async showDeleteApplicationType(interaction) {
    const config = this.loadConfig(interaction.guildId);
    const types = Object.keys(config.applicationTypes || {});

    if (types.length === 0) {
      await interaction.reply({
        content: '❌ No application types found to delete.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId('app_select_delete_type')
      .setPlaceholder('Select application type to delete...')
      .addOptions(
        types.map(typeId => {
          const type = config.applicationTypes[typeId];
          const isDefault = config.defaultApplicationType === typeId;
          return {
            label: type.name + (isDefault ? ' (Default)' : ''),
            value: typeId,
            description: `${type.questions?.length || 0} questions`,
            emoji: '🗑️'
          };
        })
      );

    const selectRow = new ActionRowBuilder().addComponents(selectMenu);

    const backButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_back_types')
          .setLabel('Back to Types')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️')
      );

    await interaction.update({
      content: '⚠️ Select an application type to delete:',
      embeds: [],
      components: [selectRow, backButton]
    });
  },

  async showSetDefaultType(interaction) {
    const config = this.loadConfig(interaction.guildId);
    const types = Object.keys(config.applicationTypes || {});

    if (types.length === 0) {
      await interaction.reply({
        content: '❌ No application types found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId('app_select_default_type')
      .setPlaceholder('Select default application type...')
      .addOptions(
        types.map(typeId => {
          const type = config.applicationTypes[typeId];
          const isDefault = config.defaultApplicationType === typeId;
          return {
            label: type.name,
            value: typeId,
            description: isDefault ? 'Currently default' : 'Set as default',
            emoji: isDefault ? '⭐' : '📋'
          };
        })
      );

    const selectRow = new ActionRowBuilder().addComponents(selectMenu);

    const backButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_back_types')
          .setLabel('Back to Types')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️')
      );

    await interaction.update({
      content: 'Select which application type should be the default:',
      embeds: [],
      components: [selectRow, backButton]
    });
  },

  async migrateLegacyQuestions(interaction) {
    const config = this.loadConfig(interaction.guildId);

    if (!config.questions || config.questions.length === 0) {
      await interaction.reply({
        content: '❌ No legacy questions found to migrate.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Create a new application type from legacy questions
    const typeId = `legacy_migrated_${Date.now()}`;
    
    if (!config.applicationTypes) {
      config.applicationTypes = {};
    }

    config.applicationTypes[typeId] = {
      name: 'Legacy Application (Migrated)',
      description: 'Migrated from legacy question system',
      questions: [...config.questions],
      messages: { ...config.messages },
      roleAssignments: { ...config.roleAssignments },
      reviewChannelId: config.adminChannelId,
      createdAt: new Date().toISOString()
    };

    // Set as default if no default exists
    if (!config.defaultApplicationType) {
      config.defaultApplicationType = typeId;
    }

    // Clear legacy questions but keep them as backup
    config.legacyQuestions = [...config.questions];
    config.questions = [];
    config.roleAssignments = {};

    this.saveConfig(interaction.guildId, config);

    await interaction.reply({
      content: '✅ Legacy questions migrated to new application type system! Use the setup command again to manage your application types.',
      flags: MessageFlags.Ephemeral
    });
  },

  // Handle select menu interactions for application types
  async handleSelectMenuInteraction(interaction) {
    const customId = interaction.customId;
    const selectedValue = interaction.values[0];

    try {
      switch (customId) {
        case 'app_select_edit_type':
          await this.showTypeEditor(interaction, selectedValue);
          break;
        case 'app_select_delete_type':
          await this.confirmDeleteType(interaction, selectedValue);
          break;
        case 'app_select_default_type':
          await this.setDefaultType(interaction, selectedValue);
          break;
        default:
          // Handle other select menus
          break;
      }
    } catch (error) {
      console.error('Error handling select menu:', error);
      await interaction.reply({
        content: 'An error occurred while processing your selection.',
        flags: MessageFlags.Ephemeral
      });
    }
  },

  async confirmDeleteType(interaction, typeId) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type) {
      await interaction.reply({
        content: '❌ Application type not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const isDefault = config.defaultApplicationType === typeId;
    const embed = new EmbedBuilder()
      .setTitle('⚠️ Delete Application Type')
      .setDescription(`Are you sure you want to delete "${type.name}"?`)
      .setColor(0xE74C3C)
      .addFields([
        {
          name: '🗑️ This will delete:',
          value: [
            `• ${type.questions?.length || 0} questions`,
            `• All role assignments for this type`,
            `• Custom messages`,
            isDefault ? '• This is the default type!' : ''
          ].filter(Boolean).join('\n'),
          inline: false
        }
      ]);

    const buttons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`app_confirm_delete_type_${typeId}`)
          .setLabel('Yes, Delete')
          .setStyle(ButtonStyle.Danger)
          .setEmoji('🗑️'),
        new ButtonBuilder()
          .setCustomId('app_back_types')
          .setLabel('Cancel')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('❌')
      );

    await interaction.update({
      embeds: [embed],
      components: [buttons]
    });
  },

  async deleteApplicationType(interaction, typeId) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type) {
      await interaction.reply({
        content: '❌ Application type not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const typeName = type.name;
    delete config.applicationTypes[typeId];

    // If this was the default type, clear the default
    if (config.defaultApplicationType === typeId) {
      config.defaultApplicationType = null;
      
      // Set a new default if other types exist
      const remainingTypes = Object.keys(config.applicationTypes);
      if (remainingTypes.length > 0) {
        config.defaultApplicationType = remainingTypes[0];
      }
    }

    this.saveConfig(interaction.guildId, config);

    await interaction.reply({
      content: `✅ Application type "${typeName}" deleted successfully!`,
      flags: MessageFlags.Ephemeral,
      components: [
        new ActionRowBuilder()
          .addComponents(
            new ButtonBuilder()
              .setCustomId('app_back_main')
              .setLabel('Back to Main Setup')
              .setStyle(ButtonStyle.Primary)
              .setEmoji('⬅️'),
            new ButtonBuilder()
              .setCustomId('app_manage_applications')
              .setLabel('Manage Applications')
              .setStyle(ButtonStyle.Secondary)
              .setEmoji('📋')
          )
      ]
    });
  },

  async setDefaultType(interaction, typeId) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type) {
      await interaction.reply({
        content: '❌ Application type not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    config.defaultApplicationType = typeId;
    this.saveConfig(interaction.guildId, config);

    await interaction.reply({
      content: `✅ "${type.name}" set as default application type! Use the setup command again to manage your application types.`,
      flags: MessageFlags.Ephemeral
    });
  },

  // Update the deployApplicationPanel method to support multiple types
  async deployMultiTypeApplicationPanel(interaction) {
    const config = this.loadConfig(interaction.guildId);

    if (!config.applicationChannelId) {
      await interaction.reply({
        content: '❌ Please set an application channel first.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    if (!config.applicationTypes || Object.keys(config.applicationTypes).length === 0) {
      await interaction.reply({
        content: '❌ Please create at least one application type first.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    try {
      const channel = await interaction.guild.channels.fetch(config.applicationChannelId);
      const types = Object.keys(config.applicationTypes);

      const embed = new EmbedBuilder()
        .setTitle('⭐ Application Portal ⭐')
        .setDescription('✨ Welcome to our official application system. Select an application type below to begin the process. ✨')
        .setColor(0x2F3136) // Dark theme matching reference
        .addFields([
          {
            name: '🌟 Application Process',
            value: '1. Select an application type below\n2. Complete the application form\n3. Submit your responses\n4. Wait for staff review',
            inline: false
          },
          {
            name: '⏱️ Estimated Time',
            value: '2-5 minutes',
            inline: true
          },
          {
            name: '🔒 Privacy Notice',
            value: 'All information provided will be kept confidential and reviewed only by authorized staff members.',
            inline: false
          }
        ])
        .setFooter({ text: `© 2025 ${interaction.guild.name} • ${new Date().toLocaleString()}` })
        .setTimestamp();

      // Create dropdown menu with all application types
      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('app_select_application_type')
        .setPlaceholder('Select an application type')
        .addOptions(
          types.map(typeId => {
            const type = config.applicationTypes[typeId];
            const isDefault = config.defaultApplicationType === typeId;
            return {
              label: type.name,
              value: typeId,
              description: type.description || `${type.questions?.length || 0} questions`,
              emoji: isDefault ? '⭐' : '🌟'
            };
          })
        );

      const selectRow = new ActionRowBuilder().addComponents(selectMenu);

      const message = await channel.send({
        embeds: [embed],
        components: [selectRow]
      });

      // Save the message ID for future reference
      config.applicationPanelMessageId = message.id;
      this.saveConfig(interaction.guildId, config);

      await interaction.reply({
        content: `✅ Multi-type application panel deployed to <#${config.applicationChannelId}>!`,
        flags: MessageFlags.Ephemeral
      });
    } catch (error) {
      console.error('Error deploying multi-type application panel:', error);
      await interaction.reply({
        content: '❌ Failed to deploy application panel. Please check the channel permissions.',
        flags: MessageFlags.Ephemeral
      });
    }
  },



  // Helper methods for simplified multi-type system
  autoMigrateToMultiType(config, guildId) {
    if (!config.applicationTypes) {
      config.applicationTypes = {};
    }

    // If there are legacy questions, create a default application type
    if (config.questions && config.questions.length > 0) {
      const defaultTypeId = 'general_application';
      config.applicationTypes[defaultTypeId] = {
        name: 'General Application',
        description: 'Standard application for server access',
        questions: [...config.questions],
        messages: config.messages || this.getDefaultConfig().messages,
        roleAssignments: config.roleAssignments || {},
        reviewChannelId: config.adminChannelId,
        createdAt: new Date().toISOString()
      };
      
      config.defaultApplicationType = defaultTypeId;
      this.saveConfig(guildId, config);
    }
  },

  getAllQuestions(config) {
    let allQuestions = [];
    
    // Get questions from new multi-type system
    if (config.applicationTypes) {
      Object.values(config.applicationTypes).forEach(type => {
        if (type.questions) {
          allQuestions = allQuestions.concat(type.questions);
        }
      });
    }
    
    // Get questions from legacy system
    if (config.questions) {
      allQuestions = allQuestions.concat(config.questions);
    }
    
    return allQuestions;
  },

  groupQuestionsByType(config) {
    const questionsByType = {};
    
    // Group questions from application types
    if (config.applicationTypes) {
      Object.entries(config.applicationTypes).forEach(([typeId, type]) => {
        if (type.questions && type.questions.length > 0) {
          questionsByType[type.name] = type.questions;
        }
      });
    }
    
    // Add legacy questions if they exist
    if (config.questions && config.questions.length > 0) {
      questionsByType['Legacy Questions'] = config.questions;
    }
    
    return questionsByType;
  },

  async showAddQuestionModal(interaction) {
    const modal = new ModalBuilder()
      .setCustomId('app_add_question_modal')
      .setTitle('Add New Question');

    const questionInput = new TextInputBuilder()
      .setCustomId('question_text')
      .setLabel('Question')
      .setStyle(TextInputStyle.Paragraph)
      .setPlaceholder('Enter your question here...')
      .setRequired(true)
      .setMaxLength(500);

    const typeInput = new TextInputBuilder()
      .setCustomId('application_type')
      .setLabel('Application Type (Optional)')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('e.g., Staff Application, Whitelist, etc. (leave blank for General)')
      .setRequired(false)
      .setMaxLength(50);

    const questionTypeInput = new TextInputBuilder()
      .setCustomId('question_type')
      .setLabel('Question Type')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('text or choice')
      .setRequired(true)
      .setMaxLength(10);

    const requiredInput = new TextInputBuilder()
      .setCustomId('is_required')
      .setLabel('Required? (yes/no)')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('yes or no')
      .setRequired(true)
      .setMaxLength(3);

    const optionsInput = new TextInputBuilder()
      .setCustomId('choice_options')
      .setLabel('Choice Options (if choice type)')
      .setStyle(TextInputStyle.Paragraph)
      .setPlaceholder('Option 1, Option 2, Option 3 (comma separated)')
      .setRequired(false)
      .setMaxLength(1000);

    modal.addComponents(
      new ActionRowBuilder().addComponents(questionInput),
      new ActionRowBuilder().addComponents(typeInput),
      new ActionRowBuilder().addComponents(questionTypeInput),
      new ActionRowBuilder().addComponents(requiredInput),
      new ActionRowBuilder().addComponents(optionsInput)
    );

    await interaction.showModal(modal);
  },

  async handleAddQuestionModal(interaction) {
    const config = this.loadConfig(interaction.guildId);
    
    const questionText = interaction.fields.getTextInputValue('question_text');
    const applicationType = interaction.fields.getTextInputValue('application_type') || 'General Application';
    const questionType = interaction.fields.getTextInputValue('question_type').toLowerCase();
    const isRequired = interaction.fields.getTextInputValue('is_required').toLowerCase() === 'yes';
    const choiceOptions = interaction.fields.getTextInputValue('choice_options');

    // Validate question type
    if (questionType !== 'text' && questionType !== 'choice') {
      await interaction.reply({
        content: '❌ Question type must be either "text" or "choice".',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Validate choice options
    if (questionType === 'choice' && !choiceOptions.trim()) {
      await interaction.reply({
        content: '❌ Choice questions must have options. Please provide comma-separated options.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Create the question object
    const newQuestion = {
      question: questionText,
      type: questionType,
      required: isRequired,
      placeholder: null,
      maxLength: 1000,
      multiline: questionType === 'text',
      verifyInChannels: false,
      verificationMode: 'none',
      logVerification: false
    };

    if (questionType === 'choice') {
      newQuestion.options = choiceOptions.split(',').map(opt => opt.trim()).filter(opt => opt);
    }

    // Initialize application types if needed
    if (!config.applicationTypes) {
      config.applicationTypes = {};
    }

    // Find or create application type
    let targetTypeId = null;
    for (const [typeId, type] of Object.entries(config.applicationTypes)) {
      if (type.name === applicationType) {
        targetTypeId = typeId;
        break;
      }
    }

    // Create new application type if it doesn't exist
    if (!targetTypeId) {
      targetTypeId = `type_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      config.applicationTypes[targetTypeId] = {
        name: applicationType,
        description: `Application type: ${applicationType}`,
        questions: [],
        messages: {
          success: {
            title: `✅ ${applicationType} Approved!`,
            description: 'Your application has been approved.',
            footer: null
          },
          denial: {
            title: `❌ ${applicationType} Denied`,
            description: 'Your application was not approved at this time.',
            footer: null
          },
          submission: {
            title: '📄 Application Summary',
            description: 'Here is a summary of your submitted application.',
            footer: 'Thank you for your submission!'
          }
        },
        roleAssignments: {},
        reviewChannelId: config.adminChannelId,
        createdAt: new Date().toISOString()
      };

      // Set as default if it's the first type
      if (!config.defaultApplicationType) {
        config.defaultApplicationType = targetTypeId;
      }
    }

    // Add question to the application type
    config.applicationTypes[targetTypeId].questions.push(newQuestion);

    this.saveConfig(interaction.guildId, config);

    await interaction.reply({
      content: `✅ Question added to "${applicationType}" successfully!`,
      flags: MessageFlags.Ephemeral
    });

    // Show questions manager again
    setTimeout(async () => {
      try {
        await this.showQuestionsManager(interaction);
      } catch (error) {
        console.log('Could not update questions manager automatically');
      }
    }, 1000);
  },

  // Update the deploy panel to work with simplified multi-type system
  async deployMultiTypeApplicationPanel(interaction) {
    const config = this.loadConfig(interaction.guildId);

    if (!config.applicationChannelId) {
      await interaction.reply({
        content: '❌ Please set an application channel first.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Auto-migrate if needed
    if (!config.applicationTypes && config.questions && config.questions.length > 0) {
      this.autoMigrateToMultiType(config, interaction.guildId);
    }

    if (!config.applicationTypes || Object.keys(config.applicationTypes).length === 0) {
      await interaction.reply({
        content: '❌ Please add some questions first.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    try {
      const channel = await interaction.guild.channels.fetch(config.applicationChannelId);
      const types = Object.keys(config.applicationTypes);
      const totalQuestions = Object.values(config.applicationTypes).reduce((sum, type) => sum + (type.questions?.length || 0), 0);

      const embed = new EmbedBuilder()
        .setTitle('⭐ Application Portal ⭐')
        .setDescription('✨ Welcome to our official application system. Select an application type below to begin the process. ✨')
        .setColor(0x2F3136) // Dark theme color matching reference design
        .addFields([
          {
            name: '🌟 Application Process',
            value: '1. Select an application type below\n2. Complete the application form\n3. Submit your responses\n4. Wait for staff review',
            inline: false
          },
          {
            name: '⏱️ Estimated Time',
            value: '2-5 minutes',
            inline: true
          },
          {
            name: '🔒 Privacy Notice',
            value: 'All information provided will be kept confidential and reviewed only by authorized staff members.',
            inline: false
          }
        ])
        .setFooter({ text: `© 2025 ${interaction.guild.name} • ${new Date().toLocaleString()}` })
        .setTimestamp();

      // Create dropdown menu with all application types
      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('app_select_application_type')
        .setPlaceholder('Select an application type')
        .addOptions(
          types.map(typeId => {
            const type = config.applicationTypes[typeId];
            const isDefault = config.defaultApplicationType === typeId;
            return {
              label: type.name,
              value: typeId,
              description: type.description || `${type.questions?.length || 0} questions`,
              emoji: isDefault ? '⭐' : '🌟'
            };
          })
        );

      const selectRow = new ActionRowBuilder().addComponents(selectMenu);

      const message = await channel.send({
        embeds: [embed],
        components: [selectRow]
      });

      // Save the message ID for future reference
      config.applicationPanelMessageId = message.id;
      this.saveConfig(interaction.guildId, config);

      await interaction.reply({
        content: `✅ Application panel deployed to <#${config.applicationChannelId}>!`,
        flags: MessageFlags.Ephemeral
      });
    } catch (error) {
      console.error('Error deploying application panel:', error);
      await interaction.reply({
        content: '❌ Failed to deploy application panel. Please check the channel permissions.',
        flags: MessageFlags.Ephemeral
      });
    }
  },



  // Create New Application Modal
  async showCreateNewApplicationModal(interaction) {
    const modal = new ModalBuilder()
      .setCustomId('app_create_new_application_modal')
      .setTitle('Create New Application');

    const nameInput = new TextInputBuilder()
      .setCustomId('application_name')
      .setLabel('Application Name')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('e.g., Staff Application, Whitelist Application')
      .setRequired(true)
      .setMaxLength(50);

    const descriptionInput = new TextInputBuilder()
      .setCustomId('application_description')
      .setLabel('Description')
      .setStyle(TextInputStyle.Paragraph)
      .setPlaceholder('Brief description of this application...')
      .setRequired(false)
      .setMaxLength(500);

    modal.addComponents(
      new ActionRowBuilder().addComponents(nameInput),
      new ActionRowBuilder().addComponents(descriptionInput)
    );

    await interaction.showModal(modal);
  },

  // Handle Create New Application Modal
  async handleCreateNewApplicationModal(interaction) {
    const config = this.loadConfig(interaction.guildId);
    
    const applicationName = interaction.fields.getTextInputValue('application_name');
    const applicationDescription = interaction.fields.getTextInputValue('application_description') || '';

    // Initialize application types if needed
    if (!config.applicationTypes) {
      config.applicationTypes = {};
    }

    // Check if application with this name already exists
    const existingType = Object.values(config.applicationTypes).find(type => type.name === applicationName);
    if (existingType) {
      await interaction.reply({
        content: `❌ An application named "${applicationName}" already exists. Please choose a different name.`,
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Generate unique ID for the application type
    const typeId = `app_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create new application type
    config.applicationTypes[typeId] = {
      name: applicationName,
      description: applicationDescription,
      questions: [],
      messages: {
        success: {
          title: `✅ ${applicationName} Approved!`,
          description: 'Your application has been approved.',
          footer: null
        },
        denial: {
          title: `❌ ${applicationName} Denied`,
          description: 'Your application was not approved at this time.',
          footer: null
        },
        submission: {
          title: '📄 Application Summary',
          description: 'Here is a summary of your submitted application.',
          footer: 'Thank you for your submission!'
        }
      },
      roleAssignments: {},
      reviewChannelId: config.adminChannelId,
      createdAt: new Date().toISOString()
    };

    // Set as default if it's the first type
    if (!config.defaultApplicationType) {
      config.defaultApplicationType = typeId;
    }

    this.saveConfig(interaction.guildId, config);

    await interaction.reply({
      content: `✅ Application "${applicationName}" created successfully! You can now add questions to it.`,
      flags: MessageFlags.Ephemeral
    });
  },

  // Manage Applications Interface
  async showManageApplications(interaction) {
    const config = this.loadConfig(interaction.guildId);
    
    // Initialize applicationTypes if it doesn't exist
    if (!config.applicationTypes) {
      config.applicationTypes = {};
      this.saveConfig(interaction.guildId, config);
    }

    const types = Object.keys(config.applicationTypes);
    
    const embed = new EmbedBuilder()
      .setTitle('📋 Manage Applications')
      .setDescription('Select an application to manage its questions and settings.')
      .setColor(0x3498DB);

    if (types.length === 0) {
      embed.addFields([
        {
          name: '📝 No Applications',
          value: 'No applications have been created yet. Click "Create New Application" to get started.',
          inline: false
        }
      ]);

      const backButton = new ActionRowBuilder()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('app_back_main')
            .setLabel('Back to Main')
            .setStyle(ButtonStyle.Secondary)
            .setEmoji('⬅️')
        );

      await interaction.update({
        embeds: [embed],
        components: [backButton]
      });
      return;
    }

    // Show list of applications
    const applicationsList = types.map(typeId => {
      const type = config.applicationTypes[typeId];
      const questionCount = type.questions ? type.questions.length : 0;
      const isDefault = config.defaultApplicationType === typeId;
      return `**${type.name}** ${isDefault ? '(Default)' : ''}\n└ ${questionCount} questions • ${type.description || 'No description'}`;
    }).join('\n\n');

    embed.addFields([
      {
        name: '📋 Your Applications',
        value: applicationsList,
        inline: false
      }
    ]);

    // Create select menu for applications
    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId('app_select_manage_application')
      .setPlaceholder('Select application to manage...')
      .addOptions(
        types.map(typeId => {
          const type = config.applicationTypes[typeId];
          const isDefault = config.defaultApplicationType === typeId;
          return {
            label: type.name + (isDefault ? ' (Default)' : ''),
            value: typeId,
            description: `${type.questions?.length || 0} questions`,
            emoji: '📋'
          };
        })
      );

    const selectRow = new ActionRowBuilder().addComponents(selectMenu);

    const backButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_back_main')
          .setLabel('Back to Main')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️')
      );

    await interaction.update({
      embeds: [embed],
      components: [selectRow, backButton]
    });
  },

  // Show specific application management
  async showApplicationManagement(interaction, typeId) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type) {
      await interaction.reply({
        content: '❌ Application not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const embed = new EmbedBuilder()
      .setTitle(`📋 Managing: ${type.name}`)
      .setDescription(type.description || 'No description')
      .setColor(0x3498DB)
      .addFields([
        {
          name: '❓ Questions',
          value: type.questions && type.questions.length > 0 
            ? type.questions.map((q, i) => {
                let status = `${q.type}${q.required ? ', required' : ', optional'}`;
                if (q.verifyInChannels) status += ', verified';
                return `${i + 1}. ${q.question} (${status})`;
              }).join('\n')
            : 'No questions added yet',
          inline: false
        }
      ]);

    const buttons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`app_add_question_to_${typeId}`)
          .setLabel('Add Question')
          .setStyle(ButtonStyle.Success)
          .setEmoji('➕'),
        new ButtonBuilder()
          .setCustomId(`app_edit_questions_${typeId}`)
          .setLabel('Edit Questions')
          .setStyle(ButtonStyle.Primary)
          .setEmoji('✏️')
          .setDisabled(!type.questions || type.questions.length === 0),
        new ButtonBuilder()
          .setCustomId(`app_role_assignments_${typeId}`)
          .setLabel('Role Assignments')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('🎭')
          .setDisabled(!type.questions || type.questions.length === 0)
      );

    const moreButtons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`app_delete_questions_${typeId}`)
          .setLabel('Delete Questions')
          .setStyle(ButtonStyle.Danger)
          .setEmoji('🗑️')
          .setDisabled(!type.questions || type.questions.length === 0)
      );

    const settingsButtons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`app_edit_app_settings_${typeId}`)
          .setLabel('Edit Application Settings')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⚙️'),
        new ButtonBuilder()
          .setCustomId(`app_save_and_deploy_${typeId}`)
          .setLabel('Save & Deploy')
          .setStyle(ButtonStyle.Success)
          .setEmoji('🚀')
          .setDisabled(!type.questions || type.questions.length === 0),
        new ButtonBuilder()
          .setCustomId(`app_delete_application_${typeId}`)
          .setLabel('Delete Application')
          .setStyle(ButtonStyle.Danger)
          .setEmoji('🗑️')
      );

    const backButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_manage_applications')
          .setLabel('Back to Applications')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️')
      );

    await interaction.update({
      embeds: [embed],
      components: [buttons, settingsButtons, backButton]
    });
  },

  // Add question to specific application
  async showAddQuestionToApplication(interaction, typeId) {
    const modal = new ModalBuilder()
      .setCustomId(`app_add_question_modal_${typeId}`)
      .setTitle('Add Question');

    const questionInput = new TextInputBuilder()
      .setCustomId('question_text')
      .setLabel('Question')
      .setStyle(TextInputStyle.Paragraph)
      .setPlaceholder('Enter your question here...')
      .setRequired(true)
      .setMaxLength(500);

    const questionTypeInput = new TextInputBuilder()
      .setCustomId('question_type')
      .setLabel('Question Type')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('text or choice')
      .setRequired(true)
      .setMaxLength(10);

    const requiredInput = new TextInputBuilder()
      .setCustomId('is_required')
      .setLabel('Required? (yes/no)')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('yes or no')
      .setRequired(true)
      .setMaxLength(3);

    const verifyInput = new TextInputBuilder()
      .setCustomId('verify_in_channels')
      .setLabel('Verify In Channels? (yes/no)')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('yes or no')
      .setRequired(true)
      .setMaxLength(3);

    const optionsInput = new TextInputBuilder()
      .setCustomId('choice_options')
      .setLabel('Choice Options (if choice type)')
      .setStyle(TextInputStyle.Paragraph)
      .setPlaceholder('Option 1, Option 2, Option 3 (comma separated)')
      .setRequired(false)
      .setMaxLength(1000);

    modal.addComponents(
      new ActionRowBuilder().addComponents(questionInput),
      new ActionRowBuilder().addComponents(questionTypeInput),
      new ActionRowBuilder().addComponents(requiredInput),
      new ActionRowBuilder().addComponents(verifyInput),
      new ActionRowBuilder().addComponents(optionsInput)
    );

    await interaction.showModal(modal);
  },

  // Handle adding question to specific application
  async handleAddQuestionToApplication(interaction, typeId) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type) {
      await interaction.reply({
        content: '❌ Application not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const questionText = interaction.fields.getTextInputValue('question_text');
    const questionType = interaction.fields.getTextInputValue('question_type').toLowerCase();
    const isRequired = interaction.fields.getTextInputValue('is_required').toLowerCase() === 'yes';
    const verifyInChannels = interaction.fields.getTextInputValue('verify_in_channels').toLowerCase() === 'yes';
    const choiceOptions = interaction.fields.getTextInputValue('choice_options');

    // Validate question type
    if (questionType !== 'text' && questionType !== 'choice') {
      await interaction.reply({
        content: '❌ Question type must be either "text" or "choice".',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Validate choice options
    if (questionType === 'choice' && !choiceOptions.trim()) {
      await interaction.reply({
        content: '❌ Choice questions must have options. Please provide comma-separated options.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Create the question object
    const newQuestion = {
      question: questionText,
      type: questionType,
      required: isRequired,
      placeholder: null,
      maxLength: 1000,
      multiline: questionType === 'text',
      verifyInChannels: verifyInChannels,
      verificationMode: verifyInChannels ? 'required' : 'none',
      logVerification: verifyInChannels
    };

    if (questionType === 'choice') {
      console.log(`[DEBUG] Raw choice options input: "${choiceOptions}"`);
      const parsedOptions = choiceOptions.split(',').map(opt => opt.trim()).filter(opt => opt);
      console.log(`[DEBUG] Parsed choice options:`, parsedOptions);
      console.log(`[DEBUG] Number of parsed options:`, parsedOptions.length);
      newQuestion.options = parsedOptions;
    }

    // Add question to the application type
    if (!type.questions) {
      type.questions = [];
    }
    type.questions.push(newQuestion);

    this.saveConfig(interaction.guildId, config);

    await interaction.reply({
      content: `✅ Question added to "${type.name}" successfully! Click the button below to continue managing this application.`,
      flags: MessageFlags.Ephemeral,
      components: [
        new ActionRowBuilder()
          .addComponents(
            new ButtonBuilder()
              .setCustomId(`app_back_to_app_${typeId}`)
              .setLabel('Continue Managing Application')
              .setStyle(ButtonStyle.Primary)
              .setEmoji('📋')
          )
      ]
    });
  },

  // Handle select menu interactions
  async handleSelectMenuInteraction(interaction) {
    const customId = interaction.customId;
    const selectedValue = interaction.values[0];

    try {
      switch (customId) {
        case 'app_select_manage_application':
          await this.showApplicationManagement(interaction, selectedValue);
          break;
        case 'app_select_channel_type':
          await this.showChannelSelectionModal(interaction, selectedValue);
          break;
        default:
          // Handle review channel selection (now using buttons instead of select menu)
          // Handle edit question selection
          if (customId.startsWith('app_select_edit_question_')) {
            const typeId = customId.replace('app_select_edit_question_', '');
            const questionIndex = parseInt(selectedValue);
            await this.showEditQuestionModal(interaction, typeId, questionIndex);
          }
          // Handle role assignment question selection
          else if (customId.startsWith('app_select_question_roles_')) {
            const typeId = customId.replace('app_select_question_roles_', '');
            const questionIndex = parseInt(selectedValue);
            await this.showRoleAssignmentForQuestion(interaction, typeId, questionIndex);
          }
          // Handle choice option role assignment
          else if (customId.startsWith('app_select_role_option_')) {
            const parts = customId.replace('app_select_role_option_', '').split('_');
            const questionIndex = parseInt(parts.pop());
            const typeId = parts.join('_');
            const option = selectedValue;
            await this.showChoiceRoleAssignmentModal(interaction, typeId, questionIndex, option);
          }
          break;
      }
    } catch (error) {
      console.error('Error handling select menu:', error);
      await interaction.reply({
        content: 'An error occurred while processing your selection.',
        flags: MessageFlags.Ephemeral
      });
    }
  },

  // Handle modal submissions
  async handleModalSubmit(interaction) {
    const customId = interaction.customId;
    console.log(`[DEBUG] Modal submission received: ${customId}`);

    try {
      // Handle message editing modals
      if (customId.startsWith('app_modal_message_')) {
        const messageType = customId.split('_')[3];
        await this.processMessageEdit(interaction, messageType);
      } else if (customId === 'app_modal_add_question') {
        await this.processAddQuestion(interaction);
      } else if (customId.startsWith('app_modal_edit_question_')) {
        const questionIndex = parseInt(customId.split('_')[4]);
        await this.processEditQuestion(interaction, questionIndex);
      } else if (customId.startsWith('app_modal_role_')) {
        await this.processRoleAssignment(interaction);
      } else if (customId === 'app_create_new_application_modal') {
        await this.handleCreateNewApplicationModal(interaction);
      } else if (customId.startsWith('app_add_question_modal_')) {
        const typeId = customId.replace('app_add_question_modal_', '');
        await this.handleAddQuestionToApplication(interaction, typeId);
      } else if (customId.startsWith('app_edit_question_modal_')) {
        const parts = customId.replace('app_edit_question_modal_', '').split('_');
        const questionIndex = parseInt(parts.pop());
        const typeId = parts.join('_');
        await this.handleEditQuestionModal(interaction, typeId, questionIndex);
      } else if (customId.startsWith('app_assign_text_role_modal_')) {
        console.log(`[DEBUG] Text role assignment modal detected: ${customId}`);
        const parts = customId.replace('app_assign_text_role_modal_', '').split('_');
        const questionIndex = parseInt(parts.pop());
        const typeId = parts.join('_');
        console.log(`[DEBUG] Parsed typeId: ${typeId}, questionIndex: ${questionIndex}`);
        await this.handleAssignTextRoleModal(interaction, typeId, questionIndex);
      } else if (customId.startsWith('app_assign_choice_role_modal_')) {
        console.log(`[DEBUG] Choice role assignment modal detected: ${customId}`);
        const parts = customId.replace('app_assign_choice_role_modal_', '').split('_');
        const option = parts.pop();
        const questionIndex = parseInt(parts.pop());
        const typeId = parts.join('_');
        console.log(`[DEBUG] Parsed typeId: ${typeId}, questionIndex: ${questionIndex}, option: ${option}`);
        await this.handleAssignChoiceRoleModal(interaction, typeId, questionIndex, option);
      } else if (customId.startsWith('app_set_review_channel_modal_')) {
        const typeId = customId.replace('app_set_review_channel_modal_', '');
        await this.handleSetReviewChannelModal(interaction, typeId);
      } else if (customId.startsWith('app_modal_channel_')) {
        await this.processChannelSelection(interaction);
      } else {
        console.log(`[DEBUG] Unhandled modal: ${customId}`);
        await interaction.reply({
          content: `❌ Unhandled modal: ${customId}`,
          flags: MessageFlags.Ephemeral
        });
      }
    } catch (error) {
      console.error('Error handling modal submission:', error);
      await interaction.reply({
        content: 'An error occurred while processing your submission.',
        flags: MessageFlags.Ephemeral
      });
    }
  },

  // Update button handler to include new buttons
  async handleSpecificButton(interaction) {
    const customId = interaction.customId;

    if (customId.startsWith('app_add_question_to_')) {
      const typeId = customId.replace('app_add_question_to_', '');
      await this.showAddQuestionToApplication(interaction, typeId);
      return;
    }

    switch (customId) {
      case 'app_back_main':
        await this.showMainSetup(interaction, true);
        break;
      case 'app_manage_applications':
        await this.showManageApplications(interaction);
        break;
      case 'app_add_question':
        await this.showAddQuestionModal(interaction);
        break;
      case 'app_edit_question':
        await this.showEditQuestionSelect(interaction);
        break;
      case 'app_delete_question':
        await this.showDeleteQuestionSelect(interaction);
        break;
      default:
        console.log(`Unhandled button: ${customId}`);
    }
  },

  // Handle delete application confirmation
  async showDeleteApplicationConfirmation(interaction, typeId) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type) {
      await interaction.reply({
        content: '❌ Application not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const embed = new EmbedBuilder()
      .setTitle('⚠️ Delete Application')
      .setDescription(`Are you sure you want to delete "${type.name}"?`)
      .setColor(0xE74C3C)
      .addFields([
        {
          name: '🗑️ This will permanently delete:',
          value: [
            `• ${type.questions?.length || 0} questions`,
            `• All role assignments for this application`,
            `• Custom messages and settings`,
            `• Application type configuration`
          ].join('\n'),
          inline: false
        },
        {
          name: '⚠️ Warning:',
          value: 'This action cannot be undone!',
          inline: false
        }
      ]);

    const buttons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`app_confirm_delete_${typeId}`)
          .setLabel('Yes, Delete Application')
          .setStyle(ButtonStyle.Danger)
          .setEmoji('🗑️'),
        new ButtonBuilder()
          .setCustomId(`app_cancel_delete_${typeId}`)
          .setLabel('Cancel')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('❌')
      );

    await interaction.update({
      embeds: [embed],
      components: [buttons]
    });
  },

  // Handle confirmed application deletion
  async deleteApplication(interaction, typeId) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type) {
      await interaction.reply({
        content: '❌ Application not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const typeName = type.name;

    // Delete the application type
    delete config.applicationTypes[typeId];

    // If this was the default type, clear the default or set a new one
    if (config.defaultApplicationType === typeId) {
      const remainingTypes = Object.keys(config.applicationTypes);
      if (remainingTypes.length > 0) {
        config.defaultApplicationType = remainingTypes[0];
      } else {
        config.defaultApplicationType = null;
      }
    }

    this.saveConfig(interaction.guildId, config);

    await interaction.reply({
      content: `✅ Application "${typeName}" deleted successfully!`,
      flags: MessageFlags.Ephemeral,
      components: [
        new ActionRowBuilder()
          .addComponents(
            new ButtonBuilder()
              .setCustomId('app_back_main')
              .setLabel('Back to Main Setup')
              .setStyle(ButtonStyle.Primary)
              .setEmoji('⬅️'),
            new ButtonBuilder()
              .setCustomId('app_manage_applications')
              .setLabel('Manage Applications')
              .setStyle(ButtonStyle.Secondary)
              .setEmoji('📋')
          )
      ]
    });
  },

  // Update handleSpecificButton to include delete application handling
  async handleSpecificButton(interaction) {
    const customId = interaction.customId;

    // Handle text role assignment button
    if (customId.startsWith('app_assign_text_role_')) {
      const parts = customId.replace('app_assign_text_role_', '').split('_');
      const questionIndex = parseInt(parts.pop());
      const typeId = parts.join('_');
      await this.showTextRoleAssignmentModal(interaction, typeId, questionIndex);
      return;
    }

    // Handle add question to specific application
    if (customId.startsWith('app_add_question_to_')) {
      const typeId = customId.replace('app_add_question_to_', '');
      await this.showAddQuestionToApplication(interaction, typeId);
      return;
    }

    // Handle edit questions for specific application
    if (customId.startsWith('app_edit_questions_')) {
      const typeId = customId.replace('app_edit_questions_', '');
      await this.showEditQuestionsForApplication(interaction, typeId);
      return;
    }

    // Handle role assignments for application
    if (customId.startsWith('app_role_assignments_')) {
      const typeId = customId.replace('app_role_assignments_', '');
      await this.showRoleAssignmentsForApplication(interaction, typeId);
      return;
    }

    // Handle edit application settings
    if (customId.startsWith('app_edit_app_settings_')) {
      const typeId = customId.replace('app_edit_app_settings_', '');
      await this.showEditApplicationSettings(interaction, typeId);
      return;
    }

    // Handle set review channel
    if (customId.startsWith('app_set_review_channel_')) {
      const typeId = customId.replace('app_set_review_channel_', '');
      await this.showSetReviewChannel(interaction, typeId);
      return;
    }

    // Handle delete application
    if (customId.startsWith('app_delete_application_')) {
      const typeId = customId.replace('app_delete_application_', '');
      await this.showDeleteApplicationConfirmation(interaction, typeId);
      return;
    }

    // Handle confirm delete application
    if (customId.startsWith('app_confirm_delete_')) {
      const typeId = customId.replace('app_confirm_delete_', '');
      await this.deleteApplication(interaction, typeId);
      return;
    }

    // Handle cancel delete application
    if (customId.startsWith('app_cancel_delete_')) {
      const typeId = customId.replace('app_cancel_delete_', '');
      await this.showApplicationManagement(interaction, typeId);
      return;
    }

    // Handle back to application
    if (customId.startsWith('app_back_to_app_')) {
      const typeId = customId.replace('app_back_to_app_', '');
      await this.showApplicationManagement(interaction, typeId);
      return;
    }

    // Handle save and deploy
    if (customId.startsWith('app_save_and_deploy_')) {
      const typeId = customId.replace('app_save_and_deploy_', '');
      await this.saveAndDeployApplication(interaction, typeId);
      return;
    }

    // Handle edit app messages
    if (customId.startsWith('app_edit_app_messages_')) {
      const typeId = customId.replace('app_edit_app_messages_', '');
      await this.showEditApplicationMessages(interaction, typeId);
      return;
    }

    // Handle edit type messages
    if (customId.startsWith('app_edit_type_messages_')) {
      const typeId = customId.replace('app_edit_type_messages_', '');
      await this.showEditApplicationMessages(interaction, typeId);
      return;
    }

    switch (customId) {
      case 'app_back_main':
        await this.showMainSetup(interaction, true);
        break;
      case 'app_manage_applications':
        await this.showManageApplications(interaction);
        break;
      case 'app_add_question':
        await this.showAddQuestionModal(interaction);
        break;
      case 'app_edit_question':
        await this.showEditQuestionSelect(interaction);
        break;
      case 'app_delete_question':
        await this.showDeleteQuestionSelect(interaction);
        break;
      default:
        console.log(`Unhandled button: ${customId}`);
    }
  },

  // Show edit questions for specific application
  async showEditQuestionsForApplication(interaction, typeId) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type) {
      await interaction.reply({
        content: '❌ Application not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    if (!type.questions || type.questions.length === 0) {
      await interaction.reply({
        content: '❌ This application has no questions to edit. Add some questions first.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const embed = new EmbedBuilder()
      .setTitle(`✏️ Edit Questions: ${type.name}`)
      .setDescription('Select a question to edit from the dropdown below.')
      .setColor(0xF39C12)
      .addFields([
        {
          name: '📋 Current Questions',
          value: type.questions.map((q, i) => 
            `**${i + 1}.** ${q.question} (${q.type}${q.required ? ', required' : ', optional'})`
          ).join('\n'),
          inline: false
        }
      ]);

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId(`app_select_edit_question_${typeId}`)
      .setPlaceholder('Select question to edit...')
      .addOptions(
        type.questions.map((q, i) => ({
          label: `Question ${i + 1}: ${q.question.substring(0, 50)}${q.question.length > 50 ? '...' : ''}`,
          value: i.toString(),
          description: `${q.type} question, ${q.required ? 'required' : 'optional'}`,
          emoji: '✏️'
        }))
      );

    const selectRow = new ActionRowBuilder().addComponents(selectMenu);

    const backButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`app_back_to_app_${typeId}`)
          .setLabel('Back to Application')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️')
      );

    await interaction.update({
      embeds: [embed],
      components: [selectRow, backButton]
    });
  },

  // Show edit application settings
  async showEditApplicationSettings(interaction, typeId) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type) {
      await interaction.reply({
        content: '❌ Application not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const embed = new EmbedBuilder()
      .setTitle(`⚙️ Application Settings: ${type.name}`)
      .setDescription('Configure settings for this application type.')
      .setColor(0x95A5A6)
      .addFields([
        {
          name: '📝 Application Name',
          value: type.name,
          inline: true
        },
        {
          name: '📄 Description',
          value: type.description || 'No description',
          inline: true
        },
        {
          name: '📊 Review Channel',
          value: type.reviewChannelId ? `<#${type.reviewChannelId}>` : 'Not set',
          inline: true
        },
        {
          name: '✅ Success Message',
          value: type.messages.success.title,
          inline: false
        },
        {
          name: '❌ Denial Message',
          value: type.messages.denial.title,
          inline: false
        }
      ]);

    const buttons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`app_edit_app_name_${typeId}`)
          .setLabel('Edit Name & Description')
          .setStyle(ButtonStyle.Primary)
          .setEmoji('📝'),
        new ButtonBuilder()
          .setCustomId(`app_edit_app_messages_${typeId}`)
          .setLabel('Edit Messages')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('💬'),
        new ButtonBuilder()
          .setCustomId(`app_set_review_channel_${typeId}`)
          .setLabel('Set Review Channel')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('📊')
      );

    const backButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`app_back_to_app_${typeId}`)
          .setLabel('Back to Application')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️')
      );

    await interaction.update({
      embeds: [embed],
      components: [buttons, backButton]
    });
  },

  // Show set review channel interface
  async showSetReviewChannel(interaction, typeId) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type) {
      await interaction.reply({
        content: '❌ Application not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const modal = new ModalBuilder()
      .setCustomId(`app_set_review_channel_modal_${typeId}`)
      .setTitle(`Set Review Channel: ${type.name}`);

    const channelInput = new TextInputBuilder()
      .setCustomId('channel_id')
      .setLabel('Channel ID')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('Enter channel ID (or leave blank for default admin channel)')
      .setRequired(false)
      .setMaxLength(20);

    const currentChannelInput = new TextInputBuilder()
      .setCustomId('current_channel')
      .setLabel('Current Channel (read-only)')
      .setStyle(TextInputStyle.Short)
      .setValue(type.reviewChannelId ? `<#${type.reviewChannelId}>` : 'Default admin channel')
      .setRequired(false);

    modal.addComponents(
      new ActionRowBuilder().addComponents(currentChannelInput),
      new ActionRowBuilder().addComponents(channelInput)
    );

    await interaction.showModal(modal);
  },

  // Handle review channel selection
  async handleReviewChannelSelection(interaction, typeId, channelId) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type) {
      await interaction.reply({
        content: '❌ Application not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    if (channelId === 'default') {
      type.reviewChannelId = null;
      await interaction.reply({
        content: `✅ Review channel for "${type.name}" set to use default admin channel.`,
        flags: MessageFlags.Ephemeral
      });
    } else {
      const channel = interaction.guild.channels.cache.get(channelId);
      if (!channel) {
        await interaction.reply({
          content: '❌ Selected channel not found.',
          flags: MessageFlags.Ephemeral
        });
        return;
      }

      type.reviewChannelId = channelId;
      await interaction.reply({
        content: `✅ Review channel for "${type.name}" set to <#${channelId}>.`,
        flags: MessageFlags.Ephemeral
      });
    }

    this.saveConfig(interaction.guildId, config);
  },

  // Show edit question modal
  async showEditQuestionModal(interaction, typeId, questionIndex) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type || !type.questions || questionIndex >= type.questions.length) {
      await interaction.reply({
        content: '❌ Question not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const question = type.questions[questionIndex];

    const modal = new ModalBuilder()
      .setCustomId(`app_edit_question_modal_${typeId}_${questionIndex}`)
      .setTitle(`Edit Question ${questionIndex + 1}`);

    const questionInput = new TextInputBuilder()
      .setCustomId('question_text')
      .setLabel('Question')
      .setStyle(TextInputStyle.Paragraph)
      .setValue(question.question)
      .setRequired(true)
      .setMaxLength(500);

    const questionTypeInput = new TextInputBuilder()
      .setCustomId('question_type')
      .setLabel('Question Type')
      .setStyle(TextInputStyle.Short)
      .setValue(question.type)
      .setPlaceholder('text or choice')
      .setRequired(true)
      .setMaxLength(10);

    const requiredInput = new TextInputBuilder()
      .setCustomId('is_required')
      .setLabel('Required? (yes/no)')
      .setStyle(TextInputStyle.Short)
      .setValue(question.required ? 'yes' : 'no')
      .setRequired(true)
      .setMaxLength(3);

    const verifyInput = new TextInputBuilder()
      .setCustomId('verify_in_channels')
      .setLabel('Verify in channels? (yes/no)')
      .setStyle(TextInputStyle.Short)
      .setValue(question.verifyInChannels ? 'yes' : 'no')
      .setPlaceholder('Should this answer be verified against server channels?')
      .setRequired(true)
      .setMaxLength(3);

    const optionsInput = new TextInputBuilder()
      .setCustomId('choice_options')
      .setLabel('Choice Options (if choice type)')
      .setStyle(TextInputStyle.Paragraph)
      .setValue(question.options ? question.options.join(', ') : '')
      .setPlaceholder('Option 1, Option 2, Option 3 (comma separated)')
      .setRequired(false)
      .setMaxLength(1000);

    modal.addComponents(
      new ActionRowBuilder().addComponents(questionInput),
      new ActionRowBuilder().addComponents(questionTypeInput),
      new ActionRowBuilder().addComponents(requiredInput),
      new ActionRowBuilder().addComponents(verifyInput),
      new ActionRowBuilder().addComponents(optionsInput)
    );

    await interaction.showModal(modal);
  },

  // Handle edit question modal submission
  async handleEditQuestionModal(interaction, typeId, questionIndex) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type || !type.questions || questionIndex >= type.questions.length) {
      await interaction.reply({
        content: '❌ Question not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const questionText = interaction.fields.getTextInputValue('question_text');
    const questionType = interaction.fields.getTextInputValue('question_type').toLowerCase();
    const isRequired = interaction.fields.getTextInputValue('is_required').toLowerCase() === 'yes';
    const verifyInChannels = interaction.fields.getTextInputValue('verify_in_channels').toLowerCase() === 'yes';
    const choiceOptions = interaction.fields.getTextInputValue('choice_options');

    // Validate question type
    if (questionType !== 'text' && questionType !== 'choice') {
      await interaction.reply({
        content: '❌ Question type must be either "text" or "choice".',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Validate choice options
    if (questionType === 'choice' && !choiceOptions.trim()) {
      await interaction.reply({
        content: '❌ Choice questions must have options. Please provide comma-separated options.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Update the question
    const updatedQuestion = {
      question: questionText,
      type: questionType,
      required: isRequired,
      placeholder: null,
      maxLength: 1000,
      multiline: questionType === 'text',
      verifyInChannels: verifyInChannels,
      verificationMode: verifyInChannels ? 'required' : 'none',
      logVerification: false
    };

    if (questionType === 'choice') {
      updatedQuestion.options = choiceOptions.split(',').map(opt => opt.trim()).filter(opt => opt);
    }

    type.questions[questionIndex] = updatedQuestion;
    this.saveConfig(interaction.guildId, config);

    await interaction.reply({
      content: `✅ Question ${questionIndex + 1} updated successfully!`,
      flags: MessageFlags.Ephemeral
    });
  },

  // Show role assignment for specific question
  async showRoleAssignmentForQuestion(interaction, typeId, questionIndex) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type || !type.questions || questionIndex >= type.questions.length) {
      await interaction.reply({
        content: '❌ Question not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const question = type.questions[questionIndex];

    const embed = new EmbedBuilder()
      .setTitle(`🎭 Role Assignment: Question ${questionIndex + 1}`)
      .setDescription(`Configure role assignment for: "${question.question}"`)
      .setColor(0xE67E22);

    // Show current role assignments
    const roleAssignments = type.roleAssignments[questionIndex] || {};
    let assignmentsList = '';
    let components = [];

    if (question.type === 'choice') {
      // Choice question: Show role assignments for each option
      if (question.options && question.options.length > 0) {
        question.options.forEach(option => {
          const roleId = roleAssignments[option];
          const roleDisplay = roleId ? `<@&${roleId}>` : 'No role assigned';
          assignmentsList += `**${option}** → ${roleDisplay}\n`;
        });
      }

      embed.addFields([
        {
          name: '📋 Choice Option Role Assignments',
          value: assignmentsList || 'No role assignments configured',
          inline: false
        },
        {
          name: 'ℹ️ How it works',
          value: 'Users who select each option will automatically receive the assigned role.',
          inline: false
        }
      ]);

      console.log(`[DEBUG] Question options for choice question:`, question.options);
      console.log(`[DEBUG] Number of options:`, question.options.length);
      
      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId(`app_select_role_option_${typeId}_${questionIndex}`)
        .setPlaceholder('Select an answer option to assign a role...')
        .addOptions(
          question.options.map((option, index) => {
            console.log(`[DEBUG] Option ${index}:`, option);
            return {
              label: option,
              value: option,
              description: `Assign role for "${option}" answer`,
              emoji: '🎭'
            };
          })
        );

      components.push(new ActionRowBuilder().addComponents(selectMenu));

    } else if (question.type === 'text') {
      // Text question: Simple single role assignment
      const hasRoleAssigned = roleAssignments['__text_answer__'];
      const assignedRoleId = hasRoleAssigned;

      assignmentsList = hasRoleAssigned ?
        `**Any answer** → <@&${assignedRoleId}>` :
        'No role assigned';

      embed.addFields([
        {
          name: '📝 Text Question Role Assignment',
          value: assignmentsList,
          inline: false
        },
        {
          name: 'ℹ️ How it works',
          value: 'Users who provide any answer to this text question will automatically receive the assigned role.',
          inline: false
        }
      ]);

      const buttonRow = new ActionRowBuilder();

      if (hasRoleAssigned) {
        buttonRow.addComponents(
          new ButtonBuilder()
            .setCustomId(`app_change_text_role_${typeId}_${questionIndex}`)
            .setLabel('Change Role')
            .setStyle(ButtonStyle.Primary)
            .setEmoji('🔄'),
          new ButtonBuilder()
            .setCustomId(`app_remove_text_role_${typeId}_${questionIndex}`)
            .setLabel('Remove Role')
            .setStyle(ButtonStyle.Danger)
            .setEmoji('🗑️')
        );
      } else {
        buttonRow.addComponents(
          new ButtonBuilder()
            .setCustomId(`app_assign_text_role_${typeId}_${questionIndex}`)
            .setLabel('Assign Role')
            .setStyle(ButtonStyle.Success)
            .setEmoji('➕')
        );
      }

      components.push(buttonRow);
    }

    const backButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`app_role_assignments_${typeId}`)
          .setLabel('Back to Role Assignments')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️')
      );

    components.push(backButton);

    await interaction.update({
      embeds: [embed],
      components: components
    });
  },

  // Show role assignments for application
  async showRoleAssignmentsForApplication(interaction, typeId) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type) {
      await interaction.reply({
        content: '❌ Application not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const embed = new EmbedBuilder()
      .setTitle(`🎭 Role Assignments: ${type.name}`)
      .setDescription('Configure automatic role assignments for questions in this application.\n\n🎭 **Choice Questions**: Assign different roles based on selected options\n📝 **Text Questions**: Assign a single role when any text answer is provided')
      .setColor(0xE67E22);

    if (!type.questions || type.questions.length === 0) {
      embed.addFields([
        {
          name: '❌ No Questions',
          value: 'You need to add questions to this application first before configuring role assignments.',
          inline: false
        }
      ]);

      const backButton = new ActionRowBuilder()
        .addComponents(
          new ButtonBuilder()
            .setCustomId(`app_back_to_app_${typeId}`)
            .setLabel('Back to Application')
            .setStyle(ButtonStyle.Secondary)
            .setEmoji('⬅️')
        );

      await interaction.update({
        embeds: [embed],
        components: [backButton]
      });
      return;
    }

    // Show all questions that support role assignments (both choice and text)
    const roleAssignmentInfo = type.questions.map((q, i) => {
      const assignments = type.roleAssignments?.[i] || {};
      let assignmentCount;
      let assignmentDetails;
      let questionTypeIcon;
      let supportIndicator;

      if (q.type === 'choice') {
        // For choice questions, count the number of option-role mappings
        assignmentCount = Object.keys(assignments).length;
        questionTypeIcon = '🎭';
        supportIndicator = '✅ Supports role assignments per choice option';
        
        if (assignmentCount > 0) {
          const roleCount = assignmentCount === 1 ? '1 role assignment' : `${assignmentCount} role assignments`;
          assignmentDetails = `└ ${roleCount} configured`;
        } else {
          assignmentDetails = '└ No role assignments configured';
        }
      } else {
        // For text questions, check if __text_answer__ key exists
        assignmentCount = assignments['__text_answer__'] ? 1 : 0;
        questionTypeIcon = '📝';
        supportIndicator = '✅ Supports single role assignment for any text answer';
        
        if (assignmentCount > 0) {
          assignmentDetails = '└ 1 role assignment configured';
        } else {
          assignmentDetails = '└ No role assignment configured';
        }
      }

      return `${questionTypeIcon} **${i + 1}.** ${q.question}\n${supportIndicator}\n${assignmentDetails}`;
    }).join('\n\n');

    embed.addFields([
      {
        name: '📋 Questions & Role Assignments',
        value: roleAssignmentInfo,
        inline: false
      }
    ]);

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId(`app_select_question_roles_${typeId}`)
      .setPlaceholder('Select question to configure roles...')
      .addOptions(
        type.questions.map((q, i) => {
          const assignments = type.roleAssignments?.[i] || {};
          let assignmentStatus;
          let description;
          
          if (q.type === 'choice') {
            const assignmentCount = Object.keys(assignments).length;
            assignmentStatus = assignmentCount > 0 ? `${assignmentCount} roles assigned` : 'No roles assigned';
            description = `Choice question - Assign roles per option (${assignmentStatus})`;
          } else {
            const hasAssignment = assignments['__text_answer__'] ? true : false;
            assignmentStatus = hasAssignment ? '1 role assigned' : 'No role assigned';
            description = `Text question - Single role for any answer (${assignmentStatus})`;
          }
          
          return {
            label: `Question ${i + 1}: ${q.question.substring(0, 45)}${q.question.length > 45 ? '...' : ''}`,
            value: i.toString(),
            description: description,
            emoji: q.type === 'choice' ? '🎭' : '📝'
          };
        })
      );

    const selectRow = new ActionRowBuilder().addComponents(selectMenu);

    const backButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`app_back_to_app_${typeId}`)
          .setLabel('Back to Application')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️')
      );

    await interaction.update({
      embeds: [embed],
      components: [selectRow, backButton]
    });
  },



  // Show text role assignment modal
  async showTextRoleAssignmentModal(interaction, typeId, questionIndex) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type || !type.questions || !type.questions[questionIndex]) {
      await interaction.reply({
        content: '❌ Question not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const question = type.questions[questionIndex];

    const modalId = `app_assign_text_role_modal_${typeId}_${questionIndex}`;
    console.log(`[DEBUG] Creating modal with ID: ${modalId}`);
    
    const modal = new ModalBuilder()
      .setCustomId(modalId)
      .setTitle(`Assign Role for Text Question`);

    const roleInput = new TextInputBuilder()
      .setCustomId('role_id')
      .setLabel('Role ID or Name')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('Enter role ID or exact role name')
      .setRequired(true);

    const answerInput = new TextInputBuilder()
      .setCustomId('answer_text')
      .setLabel('Answer Text (leave blank for any answer)')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('Specific answer text or leave blank for any text answer')
      .setRequired(false);

    modal.addComponents(
      new ActionRowBuilder().addComponents(roleInput),
      new ActionRowBuilder().addComponents(answerInput)
    );

    await interaction.showModal(modal);
  },

  // Show choice role assignment modal
  async showChoiceRoleAssignmentModal(interaction, typeId, questionIndex, option) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type || !type.questions || !type.questions[questionIndex]) {
      await interaction.reply({
        content: '❌ Question not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const question = type.questions[questionIndex];
    const modalId = `app_assign_choice_role_modal_${typeId}_${questionIndex}_${option}`;
    console.log(`[DEBUG] Creating choice role assignment modal with ID: ${modalId}`);
    
    const modal = new ModalBuilder()
      .setCustomId(modalId)
      .setTitle(`Assign Role for Choice Option`);

    const roleInput = new TextInputBuilder()
      .setCustomId('role_id')
      .setLabel('Role ID or Name')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('Enter role ID or exact role name')
      .setRequired(true);

    const optionDisplay = new TextInputBuilder()
      .setCustomId('option_display')
      .setLabel('Choice Option (read-only)')
      .setStyle(TextInputStyle.Short)
      .setValue(option)
      .setRequired(false);

    modal.addComponents(
      new ActionRowBuilder().addComponents(optionDisplay),
      new ActionRowBuilder().addComponents(roleInput)
    );

    await interaction.showModal(modal);
  },

  // Handle text role assignment modal submission
  async handleAssignTextRoleModal(interaction, typeId, questionIndex) {
    console.log(`[DEBUG] handleAssignTextRoleModal called with typeId: ${typeId}, questionIndex: ${questionIndex}`);
    console.log(`[DEBUG] Interaction state - replied: ${interaction.replied}, deferred: ${interaction.deferred}`);
    
    const config = this.loadConfig(interaction.guildId);
    console.log(`[DEBUG] Config loaded, applicationTypes:`, Object.keys(config.applicationTypes || {}));
    
    const type = config.applicationTypes[typeId];
    console.log(`[DEBUG] Type found:`, !!type);

    if (!type || !type.questions || !type.questions[questionIndex]) {
      console.log(`[DEBUG] Question validation failed - type exists: ${!!type}, questions exist: ${!!(type && type.questions)}, question index valid: ${!!(type && type.questions && type.questions[questionIndex])}`);
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: '❌ Question not found.',
          flags: MessageFlags.Ephemeral
        });
      } else {
        await interaction.followUp({
          content: '❌ Question not found.',
          flags: MessageFlags.Ephemeral
        });
      }
      return;
    }

    const roleInput = interaction.fields.getTextInputValue('role_id');
    const answerText = interaction.fields.getTextInputValue('answer_text');
    console.log(`[DEBUG] Role input: "${roleInput}", Answer text: "${answerText}"`);

    // Find the role
    let role = null;
    if (/^\d+$/.test(roleInput)) {
      // It's a role ID
      console.log(`[DEBUG] Treating as role ID`);
      role = await interaction.guild.roles.fetch(roleInput).catch((error) => {
        console.log(`[DEBUG] Failed to fetch role by ID:`, error.message);
        return null;
      });
    } else {
      // It's a role name
      console.log(`[DEBUG] Treating as role name`);
      role = interaction.guild.roles.cache.find(r => r.name.toLowerCase() === roleInput.toLowerCase());
    }
    console.log(`[DEBUG] Role found:`, !!role, role ? role.name : 'none');

    if (!role) {
      console.log(`[DEBUG] Role not found, attempting to reply`);
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: '❌ Role not found. Please check the role ID or name.',
          flags: MessageFlags.Ephemeral
        });
      } else {
        await interaction.followUp({
          content: '❌ Role not found. Please check the role ID or name.',
          flags: MessageFlags.Ephemeral
        });
      }
      return;
    }

    // Initialize role assignments if not exists
    if (!type.roleAssignments) {
      type.roleAssignments = {};
    }
    if (!type.roleAssignments[questionIndex]) {
      type.roleAssignments[questionIndex] = {};
    }

    // Set up the role assignment
    const assignmentKey = answerText.trim() || '__text_answer__';
    type.roleAssignments[questionIndex][assignmentKey] = role.id;

    this.saveConfig(interaction.guildId, config);

    const assignmentDescription = answerText.trim() 
      ? `for answer "${answerText}"`
      : 'for any text answer';

    console.log(`[DEBUG] Success! Attempting to reply with success message`);
    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({
        content: `✅ Role **${role.name}** assigned to question ${questionIndex + 1} ${assignmentDescription}.`,
        flags: MessageFlags.Ephemeral
      });
    } else {
      await interaction.followUp({
        content: `✅ Role **${role.name}** assigned to question ${questionIndex + 1} ${assignmentDescription}.`,
        flags: MessageFlags.Ephemeral
      });
    }
  },

  // Save and deploy application
  async saveAndDeployApplication(interaction, typeId) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type) {
      await interaction.reply({
        content: '❌ Application not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    if (!type.questions || type.questions.length === 0) {
      await interaction.reply({
        content: '❌ Cannot deploy application without questions. Please add at least one question first.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Configuration is already saved when questions are added
    // Now show deployment options
    const embed = new EmbedBuilder()
      .setTitle(`🚀 Deploy: ${type.name}`)
      .setDescription('Your application has been saved! Choose how you want to deploy it.')
      .setColor(0x00AE86)
      .addFields([
        {
          name: '📊 Application Summary',
          value: `**Questions:** ${type.questions.length}\n**Description:** ${type.description || 'No description'}\n**Review Channel:** ${type.reviewChannelId ? `<#${type.reviewChannelId}>` : 'Default admin channel'}`,
          inline: false
        },
        {
          name: '🚀 Deployment Options',
          value: '• **Deploy Panel** - Create an application panel in your application channel\n• **Back to Main** - Return to main setup to configure channels first',
          inline: false
        }
      ]);

    const buttons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('app_deploy_panel')
          .setLabel('Deploy Panel')
          .setStyle(ButtonStyle.Success)
          .setEmoji('🚀'),
        new ButtonBuilder()
          .setCustomId('app_back_main')
          .setLabel('Back to Main Setup')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️')
      );

    const backToAppButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`app_back_to_app_${typeId}`)
          .setLabel('Back to Application Management')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('📋')
      );

    await interaction.update({
      embeds: [embed],
      components: [buttons, backToAppButton]
    });
  },

  // Handle choice role assignment modal submission
  async handleAssignChoiceRoleModal(interaction, typeId, questionIndex, option) {
    console.log(`[DEBUG] handleAssignChoiceRoleModal called with typeId: ${typeId}, questionIndex: ${questionIndex}, option: ${option}`);
    console.log(`[DEBUG] Interaction state - replied: ${interaction.replied}, deferred: ${interaction.deferred}`);
    
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type || !type.questions || !type.questions[questionIndex]) {
      console.log(`[DEBUG] Question validation failed`);
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: '❌ Question not found.',
          flags: MessageFlags.Ephemeral
        });
      } else {
        await interaction.followUp({
          content: '❌ Question not found.',
          flags: MessageFlags.Ephemeral
        });
      }
      return;
    }

    const roleInput = interaction.fields.getTextInputValue('role_id');
    console.log(`[DEBUG] Role input: "${roleInput}", Option: "${option}"`);

    // Find the role
    let role = null;
    if (/^\d+$/.test(roleInput)) {
      // It's a role ID
      console.log(`[DEBUG] Treating as role ID`);
      role = await interaction.guild.roles.fetch(roleInput).catch((error) => {
        console.log(`[DEBUG] Failed to fetch role by ID:`, error.message);
        return null;
      });
    } else {
      // It's a role name
      console.log(`[DEBUG] Treating as role name`);
      role = interaction.guild.roles.cache.find(r => r.name.toLowerCase() === roleInput.toLowerCase());
    }
    console.log(`[DEBUG] Role found:`, !!role, role ? role.name : 'none');

    if (!role) {
      console.log(`[DEBUG] Role not found, attempting to reply`);
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: '❌ Role not found. Please check the role ID or name.',
          flags: MessageFlags.Ephemeral
        });
      } else {
        await interaction.followUp({
          content: '❌ Role not found. Please check the role ID or name.',
          flags: MessageFlags.Ephemeral
        });
      }
      return;
    }

    // Initialize role assignments if not exists
    if (!type.roleAssignments) {
      type.roleAssignments = {};
    }
    if (!type.roleAssignments[questionIndex]) {
      type.roleAssignments[questionIndex] = {};
    }

    // Set up the role assignment for this specific choice option
    type.roleAssignments[questionIndex][option] = role.id;

    this.saveConfig(interaction.guildId, config);

    console.log(`[DEBUG] Success! Attempting to reply with success message`);
    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({
        content: `✅ Role **${role.name}** assigned to choice option "${option}" for question ${questionIndex + 1}.`,
        flags: MessageFlags.Ephemeral
      });
    } else {
      await interaction.followUp({
        content: `✅ Role **${role.name}** assigned to choice option "${option}" for question ${questionIndex + 1}.`,
        flags: MessageFlags.Ephemeral
      });
    }
  },

  // Handle set review channel modal submission
  async handleSetReviewChannelModal(interaction, typeId) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type) {
      await interaction.reply({
        content: '❌ Application not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const channelId = interaction.fields.getTextInputValue('channel_id').trim();

    if (!channelId) {
      // Empty input means use default admin channel
      type.reviewChannelId = null;
      this.saveConfig(interaction.guildId, config);
      
      await interaction.reply({
        content: `✅ Review channel for "${type.name}" set to use default admin channel.`,
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Validate channel ID format
    if (!/^\d+$/.test(channelId)) {
      await interaction.reply({
        content: '❌ Invalid channel ID format. Please enter a valid numeric channel ID.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Try to fetch the channel to validate it exists
    try {
      const channel = await interaction.guild.channels.fetch(channelId);
      
      if (!channel) {
        await interaction.reply({
          content: '❌ Channel not found. Please check the channel ID.',
          flags: MessageFlags.Ephemeral
        });
        return;
      }

      if (channel.type !== 0) { // Not a text channel
        await interaction.reply({
          content: '❌ The specified channel is not a text channel. Please select a text channel.',
          flags: MessageFlags.Ephemeral
        });
        return;
      }

      // Set the review channel
      type.reviewChannelId = channelId;
      this.saveConfig(interaction.guildId, config);

      await interaction.reply({
        content: `✅ Review channel for "${type.name}" set to <#${channelId}>.`,
        flags: MessageFlags.Ephemeral
      });

    } catch (error) {
      console.error('Error fetching channel:', error);
      await interaction.reply({
        content: '❌ Failed to validate channel. Please check the channel ID and bot permissions.',
        flags: MessageFlags.Ephemeral
      });
    }
  },

  // Show edit application messages interface
  async showEditApplicationMessages(interaction, typeId) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type) {
      await interaction.reply({
        content: '❌ Application not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Ensure messages exist with defaults
    if (!type.messages) {
      const defaultConfig = this.getDefaultConfig();
      type.messages = {
        success: defaultConfig.messages.success,
        denial: defaultConfig.messages.denial
      };
      this.saveConfig(interaction.guildId, config);
    }

    const embed = new EmbedBuilder()
      .setTitle(`💬 Edit Messages: ${type.name}`)
      .setDescription('Customize the messages sent to users for this application type.')
      .setColor(0x9B59B6)
      .addFields([
        {
          name: '✅ Success Message',
          value: `**Title:** ${type.messages.success.title}\n**Description:** ${type.messages.success.description}`,
          inline: false
        },
        {
          name: '❌ Denial Message',
          value: `**Title:** ${type.messages.denial.title}\n**Description:** ${type.messages.denial.description}`,
          inline: false
        }
      ])
      .setFooter({ text: 'Click a button below to edit each message type' });

    const messageButtons = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`app_edit_success_message_${typeId}`)
          .setLabel('Edit Success Message')
          .setStyle(ButtonStyle.Success)
          .setEmoji('✅'),
        new ButtonBuilder()
          .setCustomId(`app_edit_denial_message_${typeId}`)
          .setLabel('Edit Denial Message')
          .setStyle(ButtonStyle.Danger)
          .setEmoji('❌')
      );

    const backButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`app_edit_app_settings_${typeId}`)
          .setLabel('Back to Application Settings')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️')
      );

    await interaction.update({
      embeds: [embed],
      components: [messageButtons, backButton]
    });
  }
};