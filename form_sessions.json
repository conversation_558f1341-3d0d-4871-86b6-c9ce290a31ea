{"656611572021592064": {"567939816721874957": {"guildId": "656611572021592064", "userId": "567939816721874957", "username": "crimsonop69", "typeId": "app_1753778113995_c1grbntq7", "typeName": "test3", "questions": [{"question": "name", "type": "text", "required": true, "placeholder": null, "maxLength": 1000, "multiline": true, "verifyInChannels": false, "verificationMode": "none", "logVerification": false}, {"question": "email", "type": "text", "required": true, "placeholder": null, "maxLength": 1000, "multiline": true, "verifyInChannels": true, "verificationMode": "required", "logVerification": true}, {"question": "grp", "type": "choice", "required": true, "placeholder": null, "maxLength": 1000, "multiline": false, "verifyInChannels": false, "verificationMode": "none", "logVerification": false, "options": ["grp1", "grp2"]}], "answers": {}, "verificationResults": {}, "currentQuestionIndex": 0, "useDM": false, "startTime": "2025-07-30T21:16:54.599Z", "status": "in_progress", "enhancedMode": true, "embedSequence": [{"type": "welcome", "timestamp": "2025-07-30T21:16:54.997Z", "embed": "Professional Welcome (Skipped - Confirmation shown in channel)"}, {"type": "question", "questionIndex": 0, "timestamp": "2025-07-30T21:16:58.272Z", "embed": "Question 1"}], "responseValidation": {}, "waitingForDMResponse": true, "enhancedDMMode": true, "enhancedEmbedMode": true, "lastUpdated": "2025-07-30T21:16:58.656Z"}}}